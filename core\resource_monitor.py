#!/usr/bin/env python3
"""
Resource Monitoring Service Module

This module provides centralized resource monitoring functionality to eliminate
code duplication across the TNGD backup system. It monitors CPU, memory, and
disk usage with configurable thresholds and adaptive throttling.

Features:
- CPU, memory, and disk monitoring
- Configurable thresholds and alerts
- Adaptive throttling based on resource usage
- Performance metrics collection
- Thread-safe operations
- Automatic cleanup recommendations
"""

import os
import gc
import time
import psutil
import logging
import threading
from typing import Dict, Any, Optional, Callable, List
from dataclasses import dataclass, field
from datetime import datetime, timedelta
from enum import Enum

# Configure logging
logger = logging.getLogger(__name__)


class ResourceType(Enum):
    """Types of resources to monitor."""
    CPU = "cpu"
    MEMORY = "memory"
    DISK = "disk"


class AlertLevel(Enum):
    """Alert levels for resource usage."""
    NORMAL = "normal"
    WARNING = "warning"
    CRITICAL = "critical"


@dataclass
class ResourceThresholds:
    """Resource usage thresholds."""
    warning_percent: float = 70.0
    critical_percent: float = 85.0
    cleanup_percent: float = 90.0


@dataclass
class ResourceConfig:
    """Configuration for resource monitoring."""
    cpu_thresholds: ResourceThresholds = field(default_factory=ResourceThresholds)
    memory_thresholds: ResourceThresholds = field(default_factory=ResourceThresholds)
    disk_thresholds: ResourceThresholds = field(default_factory=ResourceThresholds)
    monitoring_interval_seconds: float = 30.0
    cache_duration_seconds: float = 5.0
    adaptive_throttling_enabled: bool = True
    min_throttle_delay_seconds: float = 1.0
    max_throttle_delay_seconds: float = 60.0
    cleanup_enabled: bool = True


@dataclass
class ResourceMetrics:
    """Resource usage metrics."""
    timestamp: datetime
    cpu_percent: float
    memory_percent: float
    memory_mb: float
    disk_percent: float
    disk_free_gb: float
    alert_level: AlertLevel = AlertLevel.NORMAL
    throttle_delay: float = 0.0


@dataclass
class ResourceAlert:
    """Resource usage alert."""
    resource_type: ResourceType
    alert_level: AlertLevel
    current_usage: float
    threshold: float
    timestamp: datetime
    message: str


class ResourceMonitor:
    """Centralized resource monitoring service."""
    
    def __init__(self, config_manager=None):
        """
        Initialize the resource monitor.
        
        Args:
            config_manager: Configuration manager instance
        """
        self.config_manager = config_manager
        self.config = self._load_config()
        
        # Monitoring state
        self._last_metrics: Optional[ResourceMetrics] = None
        self._last_check_time: Optional[datetime] = None
        self._alerts: List[ResourceAlert] = []
        self._lock = threading.Lock()
        
        # Performance tracking
        self._metrics_history: List[ResourceMetrics] = []
        self._max_history_size = 100
        
        # Cleanup callbacks
        self._cleanup_callbacks: List[Callable[[], None]] = []
        
        logger.info("Resource monitor initialized")
    
    def _load_config(self) -> ResourceConfig:
        """Load resource monitoring configuration."""
        if not self.config_manager:
            return ResourceConfig()
        
        resource_config = self.config_manager.get('resource_management', {})
        
        # Load CPU thresholds
        cpu_config = resource_config.get('cpu', {})
        cpu_thresholds = ResourceThresholds(
            warning_percent=cpu_config.get('threshold_percent', 70.0),
            critical_percent=cpu_config.get('critical_threshold_percent', 85.0),
            cleanup_percent=cpu_config.get('cleanup_threshold_percent', 90.0)
        )
        
        # Load memory thresholds
        memory_config = resource_config.get('memory', {})
        memory_thresholds = ResourceThresholds(
            warning_percent=memory_config.get('threshold_percent', 60.0),
            critical_percent=memory_config.get('critical_threshold_percent', 85.0),
            cleanup_percent=memory_config.get('cleanup_threshold_percent', 90.0)
        )
        
        # Load disk thresholds
        disk_config = resource_config.get('disk', {})
        disk_thresholds = ResourceThresholds(
            warning_percent=disk_config.get('threshold_percent', 80.0),
            critical_percent=disk_config.get('critical_threshold_percent', 90.0),
            cleanup_percent=disk_config.get('cleanup_threshold_percent', 95.0)
        )
        
        # Load adaptive throttling config
        throttling_config = resource_config.get('adaptive_throttling', {})
        
        return ResourceConfig(
            cpu_thresholds=cpu_thresholds,
            memory_thresholds=memory_thresholds,
            disk_thresholds=disk_thresholds,
            monitoring_interval_seconds=resource_config.get('monitoring_interval_seconds', 30.0),
            adaptive_throttling_enabled=throttling_config.get('enabled', True),
            min_throttle_delay_seconds=throttling_config.get('min_delay_seconds', 1.0),
            max_throttle_delay_seconds=throttling_config.get('max_delay_seconds', 60.0),
            cleanup_enabled=resource_config.get('cleanup_enabled', True)
        )
    
    def get_current_metrics(self, force_refresh: bool = False) -> ResourceMetrics:
        """
        Get current resource usage metrics.
        
        Args:
            force_refresh: Force refresh even if cached data is available
            
        Returns:
            Current resource metrics
        """
        with self._lock:
            current_time = datetime.now()
            
            # Use cached metrics if available and recent
            if (not force_refresh and self._last_metrics and self._last_check_time and
                (current_time - self._last_check_time).total_seconds() < self.config.cache_duration_seconds):
                return self._last_metrics
            
            # Collect fresh metrics
            try:
                # CPU usage
                cpu_percent = psutil.cpu_percent(interval=0.1)
                
                # Memory usage
                memory = psutil.virtual_memory()
                memory_percent = memory.percent
                memory_mb = memory.used / (1024 * 1024)
                
                # Disk usage (for current working directory)
                disk = psutil.disk_usage('.')
                disk_percent = (disk.used / disk.total) * 100
                disk_free_gb = disk.free / (1024 * 1024 * 1024)
                
                # Determine alert level
                alert_level = self._determine_alert_level(cpu_percent, memory_percent, disk_percent)
                
                # Calculate throttle delay if adaptive throttling is enabled
                throttle_delay = 0.0
                if self.config.adaptive_throttling_enabled:
                    throttle_delay = self._calculate_throttle_delay(cpu_percent, memory_percent, disk_percent)
                
                metrics = ResourceMetrics(
                    timestamp=current_time,
                    cpu_percent=cpu_percent,
                    memory_percent=memory_percent,
                    memory_mb=memory_mb,
                    disk_percent=disk_percent,
                    disk_free_gb=disk_free_gb,
                    alert_level=alert_level,
                    throttle_delay=throttle_delay
                )
                
                # Cache metrics
                self._last_metrics = metrics
                self._last_check_time = current_time
                
                # Add to history
                self._add_to_history(metrics)
                
                # Check for alerts
                self._check_alerts(metrics)
                
                return metrics
                
            except Exception as e:
                logger.error(f"Error collecting resource metrics: {e}")
                # Return default metrics on error
                return ResourceMetrics(
                    timestamp=current_time,
                    cpu_percent=0.0,
                    memory_percent=0.0,
                    memory_mb=0.0,
                    disk_percent=0.0,
                    disk_free_gb=0.0
                )
    
    def _determine_alert_level(self, cpu_percent: float, memory_percent: float, disk_percent: float) -> AlertLevel:
        """Determine the overall alert level based on resource usage."""
        # Check critical thresholds
        if (cpu_percent >= self.config.cpu_thresholds.critical_percent or
            memory_percent >= self.config.memory_thresholds.critical_percent or
            disk_percent >= self.config.disk_thresholds.critical_percent):
            return AlertLevel.CRITICAL
        
        # Check warning thresholds
        if (cpu_percent >= self.config.cpu_thresholds.warning_percent or
            memory_percent >= self.config.memory_thresholds.warning_percent or
            disk_percent >= self.config.disk_thresholds.warning_percent):
            return AlertLevel.WARNING
        
        return AlertLevel.NORMAL
    
    def _calculate_throttle_delay(self, cpu_percent: float, memory_percent: float, disk_percent: float) -> float:
        """Calculate adaptive throttle delay based on resource usage."""
        if not self.config.adaptive_throttling_enabled:
            return 0.0
        
        # Calculate the maximum usage percentage across all resources
        max_usage = max(
            cpu_percent / 100.0,
            memory_percent / 100.0,
            disk_percent / 100.0
        )
        
        # No throttling if usage is below warning threshold
        warning_threshold = min(
            self.config.cpu_thresholds.warning_percent,
            self.config.memory_thresholds.warning_percent,
            self.config.disk_thresholds.warning_percent
        ) / 100.0
        
        if max_usage < warning_threshold:
            return 0.0
        
        # Calculate throttle delay based on usage above warning threshold
        usage_above_warning = max_usage - warning_threshold
        max_usage_above_warning = 1.0 - warning_threshold
        
        if max_usage_above_warning > 0:
            throttle_ratio = usage_above_warning / max_usage_above_warning
            delay = (self.config.min_throttle_delay_seconds + 
                    (self.config.max_throttle_delay_seconds - self.config.min_throttle_delay_seconds) * throttle_ratio)
            return min(delay, self.config.max_throttle_delay_seconds)
        
        return 0.0
    
    def _add_to_history(self, metrics: ResourceMetrics):
        """Add metrics to history, maintaining size limit."""
        self._metrics_history.append(metrics)
        if len(self._metrics_history) > self._max_history_size:
            self._metrics_history.pop(0)
    
    def _check_alerts(self, metrics: ResourceMetrics):
        """Check for resource usage alerts."""
        current_time = metrics.timestamp
        
        # Check CPU alerts
        if metrics.cpu_percent >= self.config.cpu_thresholds.critical_percent:
            self._add_alert(ResourceType.CPU, AlertLevel.CRITICAL, metrics.cpu_percent,
                          self.config.cpu_thresholds.critical_percent, current_time)
        elif metrics.cpu_percent >= self.config.cpu_thresholds.warning_percent:
            self._add_alert(ResourceType.CPU, AlertLevel.WARNING, metrics.cpu_percent,
                          self.config.cpu_thresholds.warning_percent, current_time)
        
        # Check memory alerts
        if metrics.memory_percent >= self.config.memory_thresholds.critical_percent:
            self._add_alert(ResourceType.MEMORY, AlertLevel.CRITICAL, metrics.memory_percent,
                          self.config.memory_thresholds.critical_percent, current_time)
        elif metrics.memory_percent >= self.config.memory_thresholds.warning_percent:
            self._add_alert(ResourceType.MEMORY, AlertLevel.WARNING, metrics.memory_percent,
                          self.config.memory_thresholds.warning_percent, current_time)
        
        # Check disk alerts
        if metrics.disk_percent >= self.config.disk_thresholds.critical_percent:
            self._add_alert(ResourceType.DISK, AlertLevel.CRITICAL, metrics.disk_percent,
                          self.config.disk_thresholds.critical_percent, current_time)
        elif metrics.disk_percent >= self.config.disk_thresholds.warning_percent:
            self._add_alert(ResourceType.DISK, AlertLevel.WARNING, metrics.disk_percent,
                          self.config.disk_thresholds.warning_percent, current_time)
    
    def _add_alert(self, resource_type: ResourceType, alert_level: AlertLevel,
                   current_usage: float, threshold: float, timestamp: datetime):
        """Add a resource usage alert."""
        message = (f"{resource_type.value.upper()} usage {alert_level.value}: "
                  f"{current_usage:.1f}% (threshold: {threshold:.1f}%)")
        
        alert = ResourceAlert(
            resource_type=resource_type,
            alert_level=alert_level,
            current_usage=current_usage,
            threshold=threshold,
            timestamp=timestamp,
            message=message
        )
        
        self._alerts.append(alert)
        
        # Log the alert
        if alert_level == AlertLevel.CRITICAL:
            logger.error(message)
        else:
            logger.warning(message)
        
        # Trigger cleanup if needed
        if (self.config.cleanup_enabled and alert_level == AlertLevel.CRITICAL and
            current_usage >= self._get_cleanup_threshold(resource_type)):
            self._trigger_cleanup(resource_type)
    
    def _get_cleanup_threshold(self, resource_type: ResourceType) -> float:
        """Get cleanup threshold for resource type."""
        if resource_type == ResourceType.CPU:
            return self.config.cpu_thresholds.cleanup_percent
        elif resource_type == ResourceType.MEMORY:
            return self.config.memory_thresholds.cleanup_percent
        elif resource_type == ResourceType.DISK:
            return self.config.disk_thresholds.cleanup_percent
        return 90.0
    
    def _trigger_cleanup(self, resource_type: ResourceType):
        """Trigger cleanup for the specified resource type."""
        logger.info(f"Triggering cleanup for {resource_type.value}")
        
        if resource_type == ResourceType.MEMORY:
            # Trigger garbage collection
            gc.collect()
            logger.info("Garbage collection triggered")
        
        # Execute registered cleanup callbacks
        for callback in self._cleanup_callbacks:
            try:
                callback()
            except Exception as e:
                logger.error(f"Error executing cleanup callback: {e}")
    
    def register_cleanup_callback(self, callback: Callable[[], None]):
        """Register a cleanup callback function."""
        self._cleanup_callbacks.append(callback)
    
    def apply_throttling(self, operation_name: str = "operation"):
        """Apply adaptive throttling delay if needed."""
        metrics = self.get_current_metrics()
        
        if metrics.throttle_delay > 0:
            logger.info(f"Applying throttling delay of {metrics.throttle_delay:.1f}s for {operation_name} "
                       f"(CPU: {metrics.cpu_percent:.1f}%, Memory: {metrics.memory_percent:.1f}%, "
                       f"Disk: {metrics.disk_percent:.1f}%)")
            time.sleep(metrics.throttle_delay)
    
    def get_recent_alerts(self, minutes: int = 60) -> List[ResourceAlert]:
        """Get recent alerts within the specified time window."""
        cutoff_time = datetime.now() - timedelta(minutes=minutes)
        return [alert for alert in self._alerts if alert.timestamp >= cutoff_time]
    
    def get_metrics_summary(self) -> Dict[str, Any]:
        """Get a summary of resource metrics."""
        current_metrics = self.get_current_metrics()
        recent_alerts = self.get_recent_alerts()
        
        return {
            'current': {
                'cpu_percent': current_metrics.cpu_percent,
                'memory_percent': current_metrics.memory_percent,
                'memory_mb': current_metrics.memory_mb,
                'disk_percent': current_metrics.disk_percent,
                'disk_free_gb': current_metrics.disk_free_gb,
                'alert_level': current_metrics.alert_level.value,
                'throttle_delay': current_metrics.throttle_delay
            },
            'alerts': {
                'recent_count': len(recent_alerts),
                'critical_count': len([a for a in recent_alerts if a.alert_level == AlertLevel.CRITICAL]),
                'warning_count': len([a for a in recent_alerts if a.alert_level == AlertLevel.WARNING])
            },
            'history_size': len(self._metrics_history),
            'cleanup_callbacks_registered': len(self._cleanup_callbacks)
        }
    
    def clear_alerts(self):
        """Clear all stored alerts."""
        with self._lock:
            self._alerts.clear()
            logger.info("Resource alerts cleared")
