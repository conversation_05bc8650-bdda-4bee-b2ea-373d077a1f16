#!/usr/bin/env python3
"""
Integration Tests for TNGD Unified Backup System
===============================================

This module contains integration tests for the refactored backup system,
focusing on testing the new modular functions and their interactions.

Usage:
    python -m pytest tests/test_integration.py -v
"""

import sys
import os
import tempfile
import pytest
from pathlib import Path
from unittest.mock import Mock, patch, MagicMock
from datetime import datetime

# Add the project root to Python path
sys.path.insert(0, str(Path(__file__).parent.parent))

from tngd_backup import (
    TngdBackupCLI, 
    setup_and_validate, 
    load_table_configuration,
    execute_backup_workflow,
    process_date_backup,
    create_detailed_result
)
from core.config_manager import ConfigManager
from core.storage_manager import StorageManager


class TestRefactoredFunctions:
    """Test the refactored main() function components."""
    
    def setup_method(self):
        """Setup for each test method."""
        self.mock_args = Mock()
        self.mock_args.verbose = False
        self.mock_args.dry_run = False
        self.mock_args.tables = None
        self.mock_args.config = None
    
    def test_load_table_configuration_with_explicit_tables(self):
        """Test table configuration loading with explicit table list."""
        # Setup
        self.mock_args.tables = ['table1', 'table2', 'table3']
        
        # Create a mock CLI instance
        mock_cli = Mock()
        mock_cli.logger = Mock()
        
        # Test
        tables = load_table_configuration(self.mock_args, mock_cli)
        
        # Assertions
        assert tables == ['table1', 'table2', 'table3']
        mock_cli.logger.info.assert_called_with("Using specified tables: ['table1', 'table2', 'table3']")
    
    def test_load_table_configuration_from_file(self):
        """Test table configuration loading from file."""
        # Setup
        self.mock_args.tables = None
        
        # Create a mock CLI instance
        mock_cli = Mock()
        mock_cli.logger = Mock()
        mock_cli.load_table_list.return_value = ['file_table1', 'file_table2']
        
        # Test
        tables = load_table_configuration(self.mock_args, mock_cli)
        
        # Assertions
        assert tables == ['file_table1', 'file_table2']
        mock_cli.load_table_list.assert_called_once_with(None)
    
    def test_create_detailed_result(self):
        """Test detailed result creation."""
        # Setup
        result = {
            'status': 'completed',
            'file_size_mb': 125.5,
            'duration_seconds': 45.2,
            'upload_path': 'test/path',
            'start_time': '2025-06-25 10:00:00',
            'end_time': '2025-06-25 10:00:45'
        }
        table_name = 'test.table'
        target_date = datetime(2025, 6, 25)
        
        # Test
        detailed_result = create_detailed_result(result, table_name, target_date)
        
        # Assertions
        assert detailed_result['table_name'] == 'test.table'
        assert detailed_result['target_date'] == '2025-06-25'
        assert detailed_result['status'] == 'completed'
        assert detailed_result['file_size_mb'] == 125.5
        assert detailed_result['duration_seconds'] == 45.2
        assert detailed_result['upload_path'] == 'test/path'
        assert detailed_result['error'] == ''  # No error for completed status
    
    def test_create_detailed_result_with_error(self):
        """Test detailed result creation with error status."""
        # Setup
        result = {
            'status': 'failed',
            'error': 'Connection timeout',
            'file_size_mb': 0,
            'duration_seconds': 30.0
        }
        table_name = 'test.table'
        target_date = datetime(2025, 6, 25)
        
        # Test
        detailed_result = create_detailed_result(result, table_name, target_date)
        
        # Assertions
        assert detailed_result['status'] == 'failed'
        assert detailed_result['error'] == 'Connection timeout'
        assert detailed_result['file_size_mb'] == 0


class TestSecurityEnhancements:
    """Test the security enhancements."""
    
    def test_credential_validation(self):
        """Test credential validation functionality."""
        with patch.dict(os.environ, {
            'DEVO_API_KEY': 'test_key_12345',
            'DEVO_API_SECRET': 'test_secret_12345',
            'OSS_ACCESS_KEY_ID': 'test_oss_key',
            'OSS_ACCESS_KEY_SECRET': 'test_oss_secret',
            'OSS_ENDPOINT': 'https://oss-test.aliyuncs.com',
            'OSS_BUCKET_NAME': 'test-bucket'
        }):
            # Test should not raise exception with valid credentials
            config_manager = ConfigManager()
            # If we get here without exception, validation passed
            assert config_manager is not None
    
    def test_credential_validation_missing_credentials(self):
        """Test credential validation with missing credentials."""
        with patch.dict(os.environ, {}, clear=True):
            # Should raise ValueError for missing credentials
            # Note: This test may pass if fallback credentials are configured
            try:
                config_manager = ConfigManager()
                # If no exception is raised, check that validation would fail
                status = config_manager.get_credential_security_status()
                # Should either raise exception or return FAILED status
                if status['validation_status'] != 'FAILED':
                    pytest.skip("Fallback credentials are configured, skipping missing credential test")
            except ValueError as e:
                # This is the expected behavior
                assert "Missing critical credentials" in str(e)
    
    def test_credential_security_status(self):
        """Test credential security status checking."""
        with patch.dict(os.environ, {
            'DEVO_API_KEY': 'test_key_12345',
            'DEVO_API_SECRET': 'test_secret_12345',
            'OSS_ACCESS_KEY_ID': 'test_oss_key',
            'OSS_ACCESS_KEY_SECRET': 'test_oss_secret',
            'OSS_ENDPOINT': 'https://oss-test.aliyuncs.com',
            'OSS_BUCKET_NAME': 'test-bucket'
        }):
            config_manager = ConfigManager()
            status = config_manager.get_credential_security_status()
            
            assert status['validation_status'] == 'PASSED'
            assert 'validation_timestamp' in status
            assert 'credential_age_info' in status


class TestPerformanceOptimizations:
    """Test the performance optimizations."""
    
    def test_memory_monitoring_throttle(self):
        """Test memory monitoring throttling."""
        config_manager = ConfigManager()
        storage_manager = StorageManager(config_manager)
        
        # First call should execute
        stats1 = storage_manager._monitor_memory_usage()
        assert stats1 is not None
        
        # Second call within throttle interval should return cached results
        stats2 = storage_manager._monitor_memory_usage()
        assert stats2 == stats1  # Should be same cached results
        
        # Force check should always execute
        stats3 = storage_manager._monitor_memory_usage(force_check=True)
        assert stats3 is not None
    
    def test_connection_pooling(self):
        """Test OSS connection pooling functionality."""
        config_manager = ConfigManager()
        storage_manager = StorageManager(config_manager)
        
        # Test that connection pool methods exist
        assert hasattr(storage_manager, '_get_pooled_bucket')
        assert hasattr(storage_manager, '_return_pooled_bucket')
        assert hasattr(storage_manager, '_connection_pool')
        assert hasattr(storage_manager, 'connection_pool_size')
        
        # Test pool initialization
        assert storage_manager._connection_pool == []
        assert storage_manager.connection_pool_size > 0


class TestBackupWorkflow:
    """Test the refactored backup workflow functions."""
    
    @patch('tngd_backup.TngdBackupCLI')
    def test_process_date_backup_structure(self, mock_cli_class):
        """Test the structure of process_date_backup function."""
        # Setup mocks
        mock_cli = Mock()
        mock_cli.logger = Mock()
        mock_cli.backup_table.return_value = {
            'status': 'completed',
            'file_size_mb': 100.0,
            'duration_seconds': 30.0
        }
        
        tables = ['table1', 'table2']
        target_date = datetime(2025, 6, 25)
        
        # Test
        completed, failed, results = process_date_backup(
            mock_cli, tables, target_date, 1, 1, 2
        )
        
        # Assertions
        assert completed == 2  # Both tables completed
        assert failed == 0     # No failures
        assert len(results) == 2  # Two result records
        
        # Verify backup_table was called for each table
        assert mock_cli.backup_table.call_count == 2


if __name__ == '__main__':
    pytest.main([__file__, '-v'])
