# Enhanced Data Retrieval Logging Guide

## Overview
This guide explains the enhanced logging system implemented for better visibility into data retrieval operations from Devo.

## Key Improvements Made

### 1. Enhanced Row Count Logging
- **Before**: Row counts were logged at DEBUG level only
- **After**: Row counts are now logged at INFO level with clear indicators
- **Benefit**: You'll always see how many rows were retrieved, even in normal operation

### 2. Categorized Data Logging
Data retrieval is now categorized by size:
- `📭 [NO_DATA]` - 0 rows found
- `✅ [SMALL_DATA]` - Less than 1,000 rows
- `📊 [MEDIUM_DATA]` - 1,000 to 99,999 rows  
- `🔥 [LARGE_DATA]` - 100,000+ rows

### 3. Performance Metrics
Each log entry now includes:
- Row count with thousands separators (e.g., `1,234,567`)
- Processing duration in seconds
- Processing rate (rows/second)
- Memory usage estimates for large datasets

### 4. Operation-Specific Logging
Different operations have distinct log prefixes:
- `[QUERY_START]` - Query initiation
- `[QUERY_PARAMS]` - Query parameters and date ranges
- `[DATA_RETRIEVED]` - Successful data retrieval
- `[CHUNK_SMALL/MEDIUM/LARGE]` - Chunked processing results
- `[MEMORY_EST]` - Memory usage estimates

## New Logging Functions

### 1. Enhanced Query Logging
```python
# In _query_table_data() function
self.logger.info(f"🚀 [QUERY_START] Querying Devo API for {table_name}")
self.logger.info(f"📅 [QUERY_PARAMS] Date range: {date_str} to {next_date_str}")
self.logger.info(f"✅ [DATA_MEDIUM] Retrieved 25,000 rows for table in 15.3s (1,634 rows/sec)")
```

### 2. Custom Data Logging Function
```python
# New utility function for detailed logging
backup_cli.log_data_retrieval(
    operation="QUERY",           # Operation type
    table_name="my.table.name",  # Table being processed
    row_count=50000,            # Number of rows
    duration=30.5,              # Processing time
    additional_info={           # Extra context
        "chunk": 2,
        "total_chunks": 5
    }
)
```

## Configuration Options

### 1. Enhanced Logging Setup
```python
# Enable enhanced data logging (default: True)
backup_cli.setup_logging(verbose=False, data_logging=True)

# Enable verbose mode for debugging
backup_cli.setup_logging(verbose=True, data_logging=True)
```

### 2. Log Levels
- **INFO**: Standard operation logs, row counts, performance metrics
- **DEBUG**: Detailed diagnostics, query text, internal operations
- **WARNING**: Issues like no data found, fallback operations
- **ERROR**: Failures and exceptions

## Example Log Output

### Before Enhancement
```
2025-06-26 09:10:53,806 - __main__ - INFO - Querying Devo API for my.app.tngd.waf...
2025-06-26 09:10:53,806 - __main__ - DEBUG - Query returned 0 rows in 2.34 seconds
```

### After Enhancement
```
2025-06-26 09:10:53,806 - __main__ - INFO - 🚀 [QUERY_START] Querying Devo API for my.app.tngd.waf
2025-06-26 09:10:53,806 - __main__ - INFO - 📅 [QUERY_PARAMS] Date range: 2025-03-01 to 2025-03-02
2025-06-26 09:10:53,806 - __main__ - INFO - ⏱️ [QUERY_CONFIG] Timeout: 21600s for table my.app.tngd.waf
2025-06-26 09:10:56,140 - __main__ - WARNING - 📭 [NO_DATA] Retrieved 0 rows for my.app.tngd.waf on 2025-03-01 (query took 2.3s)
2025-06-26 09:10:56,140 - __main__ - INFO - 🔍 [DEBUG_INFO] Check if data exists for this date range in table my.app.tngd.waf
```

## Testing the Enhanced Logging

Run the test script to see the enhanced logging in action:
```bash
python test_enhanced_logging.py
```

This will demonstrate all the different logging scenarios and show you exactly what the enhanced logging looks like.

## Best Practices

### 1. Monitor Log Files
- Check `logs/tngd_backup.log` for detailed information
- Look for `[NO_DATA]` entries to identify tables with missing data
- Monitor `[LARGE_DATA]` entries for performance optimization opportunities

### 2. Use Verbose Mode for Debugging
```bash
python tngd_backup.py --verbose "01 March 2025"
```

### 3. Filter Logs by Operation
```bash
# Find all data retrieval operations
grep "\[DATA_" logs/tngd_backup.log

# Find all no-data situations
grep "\[NO_DATA\]" logs/tngd_backup.log

# Find all large data operations
grep "\[LARGE_DATA\]" logs/tngd_backup.log
```

## Troubleshooting

### If You Don't See Row Counts
1. Check that you're running with INFO level logging (default)
2. Verify the enhanced logging is enabled
3. Look for `[DATA_RETRIEVED]` or similar tags in the logs

### If Logs Are Too Verbose
1. Disable verbose mode: Remove `--verbose` flag
2. Filter logs: Use grep to focus on specific operations
3. Adjust log levels in the configuration

## Security Notes
- Query text is logged at DEBUG level only
- Sensitive data is not logged
- Credentials are never logged
- Only metadata and row counts are logged at INFO level
