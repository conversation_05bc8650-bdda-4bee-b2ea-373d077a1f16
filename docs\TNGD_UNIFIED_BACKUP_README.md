# TNGD Unified Backup System

A unified Python-based backup solution that eliminates `.bat` file dependencies and provides a streamlined CLI interface for backing up Devo tables to OSS storage.

## 🚀 Features

- **Unified CLI Interface**: Single Python script replaces all batch files
- **Flexible Date Input**: Support for today's data, specific dates, and date ranges
- **Modular Design**: Reusable functions with robust error handling
- **Comprehensive Logging**: Structured logging with progress updates
- **Automatic Cleanup**: Safe temporary file management
- **Configuration Validation**: Built-in dry-run mode for testing

## 📋 Requirements

- Python 3.7+
- All dependencies from `requirements.txt`
- Existing core modules (`core/`, `utils/`)
- Valid Devo API credentials
- OSS storage credentials

## 🛠 Installation

The unified backup system uses the existing project structure. No additional installation is required beyond the current setup.

## 📖 Usage

### Basic Commands

```bash
# Back up today's data (yesterday for daily backups)
python tngd_backup.py

# Back up specific date
python tngd_backup.py 01 March 2025

# Back up date range
python tngd_backup.py --start "01 March 2025" --end "31 March 2025"

# Dry run (validation only)
python tngd_backup.py --dry-run

# Back up specific tables only
python tngd_backup.py --tables my.app.tngd.waf my.app.tngd.actiontraillinux

# Verbose logging
python tngd_backup.py --verbose
```

### Advanced Options

```bash
# Custom configuration file
python tngd_backup.py --config /path/to/tables.json

# Override processing parameters
python tngd_backup.py --chunk-size 50000 --timeout 3600 --max-retries 5

# Help and examples
python tngd_backup.py --help
```

## 🔧 Configuration

The system uses the existing `config.json` and `tabletest/tables.json` files:

- **Table Configuration**: Automatically detects table list from:
  - `tabletest/tables.json` (primary)
  - `config/tables.json` (fallback)
  - `backup/tables.json` (secondary fallback)

- **Backup Settings**: Uses existing configuration from `config.json`
- **Credentials**: Uses existing environment variables for Devo API and OSS

## 📊 Progress Tracking

The unified system provides real-time progress updates:

```
🚀 Starting backup process: 189 total operations
📅 Processing 3 dates with 63 tables each
============================================================

📅 Processing date 1/3: 2025-03-01
  📊 [  1/189] (  0.5%) Processing my.app.tngd.actiontraillinux... ✅ Success (2.3MB, 15.2s)
  📊 [  2/189] (  1.1%) Processing my.app.tngd.waf... ✅ Success (5.1MB, 23.4s)
  📊 [  3/189] (  1.6%) Processing cloud.office365.management.exchange... ⚠️  No data
  ...
  📋 Date 2025-03-01 completed: 58/63 successful
```

## 🎯 Migration from Batch Files

### Replaced Functionality

| Old Batch File | New Command | Description |
|----------------|-------------|-------------|
| `run_daily_backup.bat` | `python tngd_backup.py` | Daily backup operations |
| `run_monthly_backup_enhanced.bat` | `python tngd_backup.py --start "01 Mar 2025" --end "31 Mar 2025"` | Monthly backup |
| `backup_single_table.py` | `python tngd_backup.py --tables <table_name>` | Single table backup |
| All test scripts | `python tngd_backup.py --dry-run` | Validation and testing |

### Benefits of Migration

- ✅ **Simplified Deployment**: Single Python file instead of multiple batch files
- ✅ **Cross-Platform**: Works on Windows, Linux, and macOS
- ✅ **Better Error Handling**: Comprehensive exception handling and logging
- ✅ **Improved Security**: No credential exposure in batch files
- ✅ **Enhanced Monitoring**: Real-time progress and detailed reporting

## 🧪 Testing

Run the comprehensive test suite:

```bash
python test_tngd_backup.py
```

Test specific functionality:

```bash
# Test configuration loading
python tngd_backup.py --dry-run --verbose

# Test date parsing
python tngd_backup.py --dry-run 01 March 2025

# Test date range
python tngd_backup.py --dry-run --start "01 March 2025" --end "03 March 2025"

# Test specific tables
python tngd_backup.py --dry-run --tables my.app.tngd.waf
```

## 📁 File Structure

```
TNGD/
├── tngd_backup.py              # 🆕 Unified backup script
├── test_tngd_backup.py         # 🆕 Test suite
├── config.json                 # Existing configuration
├── tabletest/tables.json       # Existing table list
├── core/                       # Existing core modules
│   ├── devo_client.py
│   ├── storage_manager.py
│   └── config_manager.py
├── utils/                      # Existing utilities
└── bin/                        # 🗑️ Legacy batch files (can be removed)
```

## 🔍 Troubleshooting

### Common Issues

1. **Import Errors**: Ensure all core modules are available
2. **Configuration Not Found**: Check table configuration file paths
3. **Credential Issues**: Verify environment variables are set
4. **Date Parsing Errors**: Use supported date formats

### Debug Mode

Enable verbose logging for detailed troubleshooting:

```bash
python tngd_backup.py --dry-run --verbose
```

## 📈 Performance

The unified system maintains the same performance characteristics as the original system:

- Sequential table processing for reliability
- Chunked data extraction for large tables
- Automatic retry mechanisms
- Memory-efficient temporary file handling

## 🔒 Security

- Secure credential handling (no storage in memory)
- Safe temporary file creation and cleanup
- Input validation and sanitization
- Comprehensive error logging without credential exposure

## 📞 Support

For issues or questions:

1. Check the log files in `logs/` directory
2. Run with `--dry-run --verbose` for diagnostics
3. Review the test results from `test_tngd_backup.py`
4. Consult existing documentation in `docs/` directory
