#!/usr/bin/env python3
"""
Error Handler Module

This module provides standardized error handling for the application.
It defines custom exceptions and utility functions for consistent error handling.
"""

import logging
import traceback
import sys
from typing import Optional, Dict, Any, Callable, TypeVar, Type

# Configure logging
logger = logging.getLogger(__name__)

# Define type variables for generic functions
T = TypeVar('T')
R = TypeVar('R')

# Custom exceptions
class BackupError(Exception):
    """Base exception for all backup-related errors."""
    def __init__(self, message: str, details: Optional[Dict[str, Any]] = None):
        self.message = message
        self.details = details or {}
        super().__init__(message)


class ConfigError(BackupError):
    """Exception raised for configuration errors."""
    pass


class ApiError(BackupError):
    """Exception raised for API-related errors."""
    pass


class StorageError(BackupError):
    """Exception raised for storage-related errors."""
    pass


class ValidationError(BackupError):
    """Exception raised for validation errors."""
    pass


class NetworkError(BackupError):
    """Exception raised for network-related errors."""
    pass


# Error handling utilities
def handle_error(error: Exception, context: str = "", log_level: int = logging.ERROR) -> Dict[str, Any]:
    """
    Handle an error in a standardized way.
    
    Args:
        error: The exception to handle
        context: Additional context information
        log_level: Logging level to use
        
    Returns:
        Dictionary with error details
    """
    # Get error details
    error_type = type(error).__name__
    error_message = str(error)
    error_traceback = traceback.format_exc()
    
    # Create error details dictionary
    error_details = {
        "status": "error",
        "error_type": error_type,
        "error_message": error_message,
        "context": context
    }
    
    # Add details for BackupError
    if isinstance(error, BackupError) and error.details:
        error_details["details"] = error.details
    
    # Log the error
    log_message = f"{context}: {error_type}: {error_message}" if context else f"{error_type}: {error_message}"
    logger.log(log_level, log_message)
    
    # Log traceback at debug level
    logger.debug(f"Traceback: {error_traceback}")
    
    return error_details


def safe_execute(func: Callable[..., R], 
                error_context: str = "", 
                default_return: Optional[R] = None,
                expected_exceptions: tuple = (Exception,),
                log_level: int = logging.ERROR,
                reraise: bool = False,
                error_transformer: Optional[Callable[[Exception], Exception]] = None) -> R:
    """
    Execute a function with standardized error handling.
    
    Args:
        func: Function to execute
        error_context: Context for error messages
        default_return: Default return value if an error occurs
        expected_exceptions: Tuple of exceptions to catch
        log_level: Logging level for errors
        reraise: Whether to reraise the exception after handling
        error_transformer: Function to transform the caught exception
        
    Returns:
        Result of the function or default_return if an error occurs
    """
    try:
        return func()
    except expected_exceptions as e:
        # Transform the error if a transformer is provided
        if error_transformer:
            e = error_transformer(e)
            
        # Handle the error
        handle_error(e, error_context, log_level)
        
        # Reraise if requested
        if reraise:
            raise
            
        # Return default value
        return default_return


def convert_exception(exception: Exception, 
                     target_exception: Type[Exception], 
                     message: Optional[str] = None) -> Exception:
    """
    Convert an exception to a different type.
    
    Args:
        exception: Original exception
        target_exception: Target exception class
        message: Optional message for the new exception
        
    Returns:
        New exception of the target type
    """
    # Use original message if none provided
    if message is None:
        message = str(exception)
        
    # Create new exception
    new_exception = target_exception(message)
    
    # Copy traceback
    if hasattr(exception, '__traceback__'):
        new_exception.__traceback__ = exception.__traceback__
        
    return new_exception


def network_error_handler(func: Callable[..., T]) -> Callable[..., T]:
    """
    Decorator to handle network errors consistently.
    
    Args:
        func: Function to decorate
        
    Returns:
        Decorated function
    """
    def wrapper(*args, **kwargs):
        try:
            return func(*args, **kwargs)
        except (ConnectionError, TimeoutError) as e:
            raise NetworkError(f"Network error: {str(e)}", {"original_error": str(e)})
    return wrapper
