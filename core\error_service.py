#!/usr/bin/env python3
"""
Enhanced Error Service Module

This module provides centralized error handling and management for the TNGD backup system.
It consolidates error handling patterns, provides error classification, and manages
error recovery strategies.

Features:
- Centralized error handling patterns
- Error classification and severity assessment
- Error context management
- Recovery strategy recommendations
- Error metrics and reporting
- Thread-safe error tracking
"""

import logging
import traceback
import threading
from typing import Any, Dict, Optional, Callable, TypeVar, List, Union
from datetime import datetime, timedelta
from enum import Enum
from dataclasses import dataclass, field
from collections import defaultdict

# Import existing error classes
from utils.error_handler import (
    BackupError, ConfigError, ApiError, StorageError, 
    ValidationError, NetworkError
)

# Configure logging
logger = logging.getLogger(__name__)

# Type variable for generic functions
T = TypeVar('T')


class ErrorSeverity(Enum):
    """Error severity levels."""
    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"
    CRITICAL = "critical"


class ErrorCategory(Enum):
    """Error categories for classification."""
    CONFIGURATION = "configuration"
    NETWORK = "network"
    STORAGE = "storage"
    DATA_PROCESSING = "data_processing"
    AUTHENTICATION = "authentication"
    VALIDATION = "validation"
    RESOURCE = "resource"
    API = "api"
    UNKNOWN = "unknown"


class RecoveryStrategy(Enum):
    """Recovery strategies for different error types."""
    RETRY = "retry"
    FALLBACK = "fallback"
    SKIP = "skip"
    ABORT = "abort"
    MANUAL_INTERVENTION = "manual_intervention"


@dataclass
class ErrorContext:
    """Context information for errors."""
    operation: str
    component: str
    table_name: Optional[str] = None
    date: Optional[str] = None
    attempt: int = 1
    max_attempts: int = 1
    additional_info: Dict[str, Any] = field(default_factory=dict)


@dataclass
class ErrorRecord:
    """Record of an error occurrence."""
    timestamp: datetime
    error_type: str
    error_message: str
    severity: ErrorSeverity
    category: ErrorCategory
    context: ErrorContext
    traceback: Optional[str] = None
    resolved: bool = False
    resolution_notes: Optional[str] = None
    recovery_strategy: Optional[RecoveryStrategy] = None


@dataclass
class ErrorPattern:
    """Pattern for error classification and handling."""
    error_keywords: List[str]
    category: ErrorCategory
    severity: ErrorSeverity
    recovery_strategy: RecoveryStrategy
    description: str


class ErrorService:
    """Centralized error handling and management service."""
    
    def __init__(self, config_manager=None):
        """
        Initialize the error service.
        
        Args:
            config_manager: Configuration manager instance
        """
        self.config_manager = config_manager
        self._error_records: List[ErrorRecord] = []
        self._error_patterns = self._initialize_error_patterns()
        self._error_stats = defaultdict(int)
        self._lock = threading.Lock()
        
        logger.info("Error service initialized")
    
    def _initialize_error_patterns(self) -> List[ErrorPattern]:
        """Initialize error patterns for classification."""
        return [
            # Network errors
            ErrorPattern(
                error_keywords=['timeout', 'connection', 'network', 'dns', 'socket'],
                category=ErrorCategory.NETWORK,
                severity=ErrorSeverity.MEDIUM,
                recovery_strategy=RecoveryStrategy.RETRY,
                description="Network connectivity issues"
            ),
            
            # Storage errors
            ErrorPattern(
                error_keywords=['disk', 'space', 'permission', 'file not found', 'access denied'],
                category=ErrorCategory.STORAGE,
                severity=ErrorSeverity.HIGH,
                recovery_strategy=RecoveryStrategy.MANUAL_INTERVENTION,
                description="Storage and file system issues"
            ),
            
            # Authentication errors
            ErrorPattern(
                error_keywords=['unauthorized', 'forbidden', 'authentication', 'credential', 'token'],
                category=ErrorCategory.AUTHENTICATION,
                severity=ErrorSeverity.CRITICAL,
                recovery_strategy=RecoveryStrategy.MANUAL_INTERVENTION,
                description="Authentication and authorization issues"
            ),
            
            # Configuration errors
            ErrorPattern(
                error_keywords=['config', 'setting', 'parameter', 'invalid format', 'missing'],
                category=ErrorCategory.CONFIGURATION,
                severity=ErrorSeverity.HIGH,
                recovery_strategy=RecoveryStrategy.MANUAL_INTERVENTION,
                description="Configuration and setup issues"
            ),
            
            # Data processing errors
            ErrorPattern(
                error_keywords=['parse', 'format', 'decode', 'invalid data', 'corruption'],
                category=ErrorCategory.DATA_PROCESSING,
                severity=ErrorSeverity.MEDIUM,
                recovery_strategy=RecoveryStrategy.SKIP,
                description="Data processing and format issues"
            ),
            
            # Resource errors
            ErrorPattern(
                error_keywords=['memory', 'cpu', 'resource', 'limit', 'quota'],
                category=ErrorCategory.RESOURCE,
                severity=ErrorSeverity.HIGH,
                recovery_strategy=RecoveryStrategy.FALLBACK,
                description="Resource limitation issues"
            ),
            
            # API errors
            ErrorPattern(
                error_keywords=['api', 'service unavailable', 'rate limit', 'throttle'],
                category=ErrorCategory.API,
                severity=ErrorSeverity.MEDIUM,
                recovery_strategy=RecoveryStrategy.RETRY,
                description="API and service issues"
            )
        ]
    
    def handle_error(self, error: Exception, context: ErrorContext, 
                    log_level: int = logging.ERROR) -> ErrorRecord:
        """
        Handle an error with comprehensive classification and logging.
        
        Args:
            error: The exception to handle
            context: Error context information
            log_level: Logging level to use
            
        Returns:
            ErrorRecord with error details and classification
        """
        with self._lock:
            # Classify the error
            category, severity, recovery_strategy = self._classify_error(error)
            
            # Create error record
            error_record = ErrorRecord(
                timestamp=datetime.now(),
                error_type=type(error).__name__,
                error_message=str(error),
                severity=severity,
                category=category,
                context=context,
                traceback=traceback.format_exc(),
                recovery_strategy=recovery_strategy
            )
            
            # Store error record
            self._error_records.append(error_record)
            
            # Update statistics
            self._error_stats[f"{category.value}_{severity.value}"] += 1
            self._error_stats["total_errors"] += 1
            
            # Log the error
            self._log_error(error_record, log_level)
            
            return error_record
    
    def _classify_error(self, error: Exception) -> tuple[ErrorCategory, ErrorSeverity, RecoveryStrategy]:
        """Classify an error based on patterns and type."""
        error_message = str(error).lower()
        error_type = type(error).__name__
        
        # Check for specific error types first
        if isinstance(error, ConfigError):
            return ErrorCategory.CONFIGURATION, ErrorSeverity.HIGH, RecoveryStrategy.MANUAL_INTERVENTION
        elif isinstance(error, NetworkError):
            return ErrorCategory.NETWORK, ErrorSeverity.MEDIUM, RecoveryStrategy.RETRY
        elif isinstance(error, StorageError):
            return ErrorCategory.STORAGE, ErrorSeverity.HIGH, RecoveryStrategy.MANUAL_INTERVENTION
        elif isinstance(error, ValidationError):
            return ErrorCategory.VALIDATION, ErrorSeverity.MEDIUM, RecoveryStrategy.SKIP
        elif isinstance(error, ApiError):
            return ErrorCategory.API, ErrorSeverity.MEDIUM, RecoveryStrategy.RETRY
        
        # Check error patterns
        for pattern in self._error_patterns:
            if any(keyword in error_message for keyword in pattern.error_keywords):
                return pattern.category, pattern.severity, pattern.recovery_strategy
        
        # Default classification
        return ErrorCategory.UNKNOWN, ErrorSeverity.MEDIUM, RecoveryStrategy.RETRY
    
    def _log_error(self, error_record: ErrorRecord, log_level: int):
        """Log an error record with appropriate formatting."""
        context = error_record.context
        
        # Build context string
        context_parts = [f"Operation: {context.operation}", f"Component: {context.component}"]
        if context.table_name:
            context_parts.append(f"Table: {context.table_name}")
        if context.date:
            context_parts.append(f"Date: {context.date}")
        if context.attempt > 1:
            context_parts.append(f"Attempt: {context.attempt}/{context.max_attempts}")
        
        context_str = " | ".join(context_parts)
        
        # Build log message
        log_message = (f"[{error_record.severity.value.upper()}] "
                      f"{error_record.error_type}: {error_record.error_message} "
                      f"| {context_str} "
                      f"| Category: {error_record.category.value} "
                      f"| Recovery: {error_record.recovery_strategy.value if error_record.recovery_strategy else 'unknown'}")
        
        # Log with appropriate level
        logger.log(log_level, log_message)
        
        # Log traceback at debug level for high/critical errors
        if error_record.severity in [ErrorSeverity.HIGH, ErrorSeverity.CRITICAL]:
            logger.debug(f"Traceback for {error_record.error_type}: {error_record.traceback}")
    
    def get_recovery_recommendation(self, error: Exception, context: ErrorContext) -> RecoveryStrategy:
        """Get recovery strategy recommendation for an error."""
        _, _, recovery_strategy = self._classify_error(error)
        return recovery_strategy
    
    def should_retry(self, error: Exception, context: ErrorContext) -> bool:
        """Determine if an operation should be retried based on error classification."""
        recovery_strategy = self.get_recovery_recommendation(error, context)
        return recovery_strategy == RecoveryStrategy.RETRY
    
    def should_skip(self, error: Exception, context: ErrorContext) -> bool:
        """Determine if an operation should be skipped based on error classification."""
        recovery_strategy = self.get_recovery_recommendation(error, context)
        return recovery_strategy == RecoveryStrategy.SKIP
    
    def should_abort(self, error: Exception, context: ErrorContext) -> bool:
        """Determine if an operation should be aborted based on error classification."""
        recovery_strategy = self.get_recovery_recommendation(error, context)
        return recovery_strategy == RecoveryStrategy.ABORT
    
    def get_recent_errors(self, minutes: int = 60, 
                         severity_filter: Optional[ErrorSeverity] = None,
                         category_filter: Optional[ErrorCategory] = None) -> List[ErrorRecord]:
        """Get recent errors with optional filtering."""
        cutoff_time = datetime.now() - timedelta(minutes=minutes)
        
        filtered_errors = []
        for error_record in self._error_records:
            if error_record.timestamp < cutoff_time:
                continue
            
            if severity_filter and error_record.severity != severity_filter:
                continue
                
            if category_filter and error_record.category != category_filter:
                continue
            
            filtered_errors.append(error_record)
        
        return filtered_errors
    
    def get_error_summary(self) -> Dict[str, Any]:
        """Get a summary of error statistics."""
        with self._lock:
            recent_errors = self.get_recent_errors(60)  # Last hour
            critical_errors = [e for e in recent_errors if e.severity == ErrorSeverity.CRITICAL]
            high_errors = [e for e in recent_errors if e.severity == ErrorSeverity.HIGH]
            
            return {
                'total_errors': len(self._error_records),
                'recent_errors_count': len(recent_errors),
                'critical_errors_count': len(critical_errors),
                'high_severity_errors_count': len(high_errors),
                'error_categories': dict(self._error_stats),
                'most_common_categories': self._get_most_common_categories(),
                'recovery_strategies': self._get_recovery_strategy_stats()
            }
    
    def _get_most_common_categories(self) -> List[tuple[str, int]]:
        """Get most common error categories."""
        category_counts = defaultdict(int)
        for error_record in self._error_records:
            category_counts[error_record.category.value] += 1
        
        return sorted(category_counts.items(), key=lambda x: x[1], reverse=True)[:5]
    
    def _get_recovery_strategy_stats(self) -> Dict[str, int]:
        """Get recovery strategy statistics."""
        strategy_counts = defaultdict(int)
        for error_record in self._error_records:
            if error_record.recovery_strategy:
                strategy_counts[error_record.recovery_strategy.value] += 1
        
        return dict(strategy_counts)
    
    def clear_old_errors(self, days: int = 7):
        """Clear error records older than specified days."""
        cutoff_time = datetime.now() - timedelta(days=days)
        
        with self._lock:
            initial_count = len(self._error_records)
            self._error_records = [e for e in self._error_records if e.timestamp >= cutoff_time]
            cleared_count = initial_count - len(self._error_records)
            
            if cleared_count > 0:
                logger.info(f"Cleared {cleared_count} old error records (older than {days} days)")
    
    def mark_error_resolved(self, error_record: ErrorRecord, resolution_notes: str):
        """Mark an error as resolved with notes."""
        error_record.resolved = True
        error_record.resolution_notes = resolution_notes
        logger.info(f"Error marked as resolved: {error_record.error_type} - {resolution_notes}")
