"""
Minimalist Logging Utility

A simplified logging system that focuses on essential information while reducing noise.
"""

import os
import sys
import logging
import logging.handlers
import datetime
import json
from typing import Optional, Dict, Any

# Constants
DEFAULT_LOG_DIR = 'logs'
DEFAULT_LOG_FILE = 'backup.log'
DEFAULT_MAX_SIZE_MB = 10
DEFAULT_MAX_FILES = 5

class MinimalLogger:
    """
    Minimalist logger that focuses on essential information.
    """
    
    def __init__(self):
        self._logger = None
        self._setup_logger()

    def _setup_logger(self):
        """Configure the minimal logger."""
        try:
            # Create logs directory if it doesn't exist
            os.makedirs(DEFAULT_LOG_DIR, exist_ok=True)

            # Create root logger
            self._logger = logging.getLogger('minimal_logger')
            self._logger.setLevel(logging.INFO)

            # Remove any existing handlers
            for handler in self._logger.handlers[:]:
                self._logger.removeHandler(handler)

            # Console handler with minimal formatting
            console_handler = logging.StreamHandler(stream=sys.stdout)
            console_handler.setFormatter(logging.Formatter('[%(levelname)s] %(message)s'))
            self._logger.addHandler(console_handler)

            # File handler with rotation
            log_file = os.path.join(DEFAULT_LOG_DIR, DEFAULT_LOG_FILE)
            file_handler = logging.handlers.RotatingFileHandler(
                log_file,
                maxBytes=DEFAULT_MAX_SIZE_MB * 1024 * 1024,
                backupCount=DEFAULT_MAX_FILES,
                encoding='utf-8'
            )
            file_handler.setFormatter(
                logging.Formatter('%(asctime)s [%(levelname)s] %(message)s', 
                                datefmt='%Y-%m-%d %H:%M:%S')
            )
            self._logger.addHandler(file_handler)

            # Log startup message
            self._logger.info(f"Logging initialized - {datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
            self._logger.info(f"Log file: {log_file}")

        except Exception as e:
            print(f"Error setting up logger: {str(e)}", file=sys.stderr)
            raise

    def info(self, message: str) -> None:
        """Log info message."""
        self._logger.info(message)

    def error(self, message: str) -> None:
        """Log error message."""
        self._logger.error(message)

    def warning(self, message: str) -> None:
        """Log warning message."""
        self._logger.warning(message)

    def debug(self, message: str) -> None:
        """Log debug message."""
        self._logger.debug(message)

    def log_operation(self, operation: str, status: str, details: Optional[str] = None, 
                      component: Optional[str] = None, phase: Optional[str] = None) -> None:
        """
        Log an operation with its status and optional details in structured format.
        
        Args:
            operation: Name of the operation
            status: Status (INIT, PROCESS, SUCCESS, ERROR, FINISH, etc.)
            details: Optional additional details
            component: Optional component name
            phase: Optional phase tag (INIT, PROCESS, SUCCESS, ERROR, FINISH)
        """
        log_entry = {
            "timestamp": datetime.datetime.now().isoformat(),
            "level": "INFO",
            "operation": operation,
            "status": status,
            "component": component or "general",
            "phase": phase or status,
            "message": details or ""
        }
        message = self._format_log_entry(log_entry)

        if status.upper() == "ERROR" or status.upper() == "FAILED":
            log_entry["level"] = "ERROR"
            self._logger.error(message)
        elif status.upper() == "WARNING":
            log_entry["level"] = "WARNING"
            self._logger.warning(message)
        else:
            self._logger.info(message)

    def log_structured(self, level: str, component: str, phase: str, message: str, 
                       metadata: Optional[Dict[str, Any]] = None) -> None:
        """
        Log a structured message with key-value pairs suitable for parsing.

        Args:
            level: Log level (INFO, WARNING, ERROR)
            component: Component or module name
            phase: Phase tag (INIT, PROCESS, SUCCESS, ERROR, FINISH)
            message: Main log message
            metadata: Optional additional key-value data
        """
        log_entry = {
            "timestamp": datetime.datetime.now().isoformat(),
            "level": level.upper(),
            "component": component,
            "phase": phase,
            "message": message
        }
        if metadata:
            log_entry.update(metadata)

        formatted_message = self._format_log_entry(log_entry)

        if level.upper() == "ERROR":
            self._logger.error(formatted_message)
        elif level.upper() == "WARNING":
            self._logger.warning(formatted_message)
        else:
            self._logger.info(formatted_message)

    def _format_log_entry(self, log_entry: Dict[str, Any]) -> str:
        """
        Format the log entry as a key=value string for easy parsing.

        Args:
            log_entry: Dictionary of log fields

        Returns:
            Formatted string
        """
        # Format as key=value pairs separated by space, escape spaces in values with quotes
        parts = []
        for key, value in log_entry.items():
            val_str = str(value)
            if " " in val_str or ":" in val_str or "," in val_str:
                val_str = f'"{val_str}"'
            parts.append(f"{key}={val_str}")
        return " ".join(parts)

    def log_backup_summary(self, successful: int, failed: int, duration: float) -> None:
        """
        Log a concise backup summary.
        
        Args:
            successful: Number of successful operations
            failed: Number of failed operations
            duration: Duration in seconds
        """
        total = successful + failed
        success_rate = (successful / total * 100) if total > 0 else 0
        
        self._logger.info("-" * 50)
        self._logger.info("Backup Summary:")
        self._logger.info(f"Success Rate: {success_rate:.1f}% ({successful}/{total})")
        self._logger.info(f"Duration: {self._format_duration(duration)}")
        if failed > 0:
            self._logger.warning(f"Failed Operations: {failed}")
        self._logger.info("-" * 50)

    @staticmethod
    def _format_duration(seconds: float) -> str:
        """Format duration in a human-readable format."""
        if seconds < 60:
            return f"{seconds:.1f} seconds"
        elif seconds < 3600:
            minutes = seconds // 60
            remaining_seconds = seconds % 60
            return f"{int(minutes)}m {int(remaining_seconds)}s"
        else:
            hours = seconds // 3600
            minutes = (seconds % 3600) // 60
            return f"{int(hours)}h {int(minutes)}m"

# Global logger instance
logger = MinimalLogger()
