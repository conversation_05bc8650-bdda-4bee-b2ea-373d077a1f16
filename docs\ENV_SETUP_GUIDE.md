# TNGD Backup System - Environment Setup Guide

## 🔐 Setting Up Credentials with .env Files

The TNGD unified backup system uses `.env` files for secure credential management. This guide will help you set up your credentials properly.

## 📋 Quick Setup

### 1. Create Your .env File

```bash
# Copy the template to create your .env file
cp .env.template .env
```

### 2. Edit the .env File

Open `.env` in your text editor and fill in your actual credentials:

```bash
# Required Devo API credentials
DEVO_API_KEY=your_actual_api_key_here
DEVO_API_SECRET=your_actual_api_secret_here
DEVO_QUERY_ENDPOINT=https://api-us.devo.com/search/query

# Required OSS storage credentials
OSS_ACCESS_KEY_ID=your_actual_access_key_id
OSS_ACCESS_KEY_SECRET=your_actual_access_key_secret
OSS_ENDPOINT=your_actual_oss_endpoint
OSS_BUCKET_NAME=your_actual_bucket_name
```

### 3. Test Your Configuration

```bash
# Test with dry-run mode
python tngd_backup.py --dry-run --verbose
```

## 🔑 Required Credentials

### Devo API Credentials

| Variable | Description | Example |
|----------|-------------|---------|
| `DEVO_API_KEY` | Your Devo API key | `abcd1234-5678-90ef-ghij-klmnopqrstuv` |
| `DEVO_API_SECRET` | Your Devo API secret | `wxyz9876-5432-10ab-cdef-ghijklmnopqr` |
| `DEVO_QUERY_ENDPOINT` | Devo query endpoint URL | `https://api-us.devo.com/search/query` |

### OSS Storage Credentials

| Variable | Description | Example |
|----------|-------------|---------|
| `OSS_ACCESS_KEY_ID` | OSS access key ID | `LTAI4GxxxxxxxxxxxxxxK7` |
| `OSS_ACCESS_KEY_SECRET` | OSS access key secret | `3Bxxxxxxxxxxxxxxxxxxxxxxxx2F` |
| `OSS_ENDPOINT` | OSS endpoint URL | `https://oss-us-east-1.aliyuncs.com` |
| `OSS_BUCKET_NAME` | OSS bucket name | `tngd-backup-storage` |

## 🛡️ Security Best Practices

### 1. Protect Your .env File

```bash
# Set proper file permissions (Unix/Linux/macOS)
chmod 600 .env

# Add .env to .gitignore to prevent accidental commits
echo ".env" >> .gitignore
```

### 2. Never Commit Credentials

- ✅ **DO**: Use `.env.template` for documentation
- ✅ **DO**: Add `.env` to `.gitignore`
- ❌ **DON'T**: Commit actual `.env` files to version control
- ❌ **DON'T**: Share credentials in chat/email

### 3. Use Environment-Specific Files

```bash
.env.development    # Development credentials
.env.staging        # Staging credentials  
.env.production     # Production credentials
```

## 🧪 Testing Your Setup

### 1. Basic Connectivity Test

```bash
# Test configuration loading and API connectivity
python tngd_backup.py --dry-run
```

Expected output:
```
✅ Configuration loaded successfully
✅ Devo API client initialized
✅ Storage manager initialized
✅ All clients initialized successfully
```

### 2. Credential Validation

```bash
# Test with verbose logging to see detailed information
python tngd_backup.py --dry-run --verbose
```

### 3. Table Loading Test

```bash
# Test specific table access
python tngd_backup.py --dry-run --tables my.app.tngd.waf
```

## 🔧 Troubleshooting

### Common Issues

#### 1. "Devo API credentials not found"

**Problem**: Missing or incorrect Devo credentials

**Solution**:
```bash
# Check if .env file exists
ls -la .env

# Verify credentials are set
grep DEVO_ .env
```

#### 2. "OSS credentials not found"

**Problem**: Missing OSS storage credentials

**Solution**:
```bash
# Check OSS credentials
grep OSS_ .env
```

#### 3. ".env file not loaded"

**Problem**: .env file not in the correct location

**Solution**:
```bash
# Ensure .env is in the project root directory
pwd
ls -la .env
```

### Debug Commands

```bash
# Check environment variables are loaded
python -c "from dotenv import load_dotenv; load_dotenv(); import os; print('DEVO_API_KEY:', 'SET' if os.getenv('DEVO_API_KEY') else 'NOT SET')"

# Test configuration manager
python -c "from core.config_manager import ConfigManager; cm = ConfigManager(); print('Config loaded successfully')"

# Test Devo client initialization
python -c "from core.devo_client import DevoClient; client = DevoClient(); print('Devo client initialized')"
```

## 📁 File Structure

```
TNGD/
├── .env                    # 🔐 Your actual credentials (DO NOT COMMIT)
├── .env.template          # 📋 Template with examples
├── .gitignore             # 🛡️ Should include .env
├── tngd_backup.py         # 🚀 Main backup script
└── ENV_SETUP_GUIDE.md     # 📖 This guide
```

## 🎯 Next Steps

Once your `.env` file is configured:

1. **Test the setup**: `python tngd_backup.py --dry-run`
2. **Run a small backup**: `python tngd_backup.py --tables my.app.tngd.waf`
3. **Schedule regular backups**: Set up cron jobs or task scheduler
4. **Monitor logs**: Check `logs/tngd_backup.log` for detailed information

## 📞 Support

If you encounter issues:

1. Run with verbose logging: `python tngd_backup.py --dry-run --verbose`
2. Check the log file: `logs/tngd_backup.log`
3. Verify your credentials with your Devo and OSS administrators
4. Ensure all required Python packages are installed: `pip install -r requirements.txt`
