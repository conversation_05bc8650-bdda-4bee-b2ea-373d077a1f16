#!/usr/bin/env python3
"""
Test script for TNGD Unified Backup System
==========================================

This script tests various aspects of the unified backup system including:
- CLI argument parsing
- Configuration loading
- Date parsing and range generation
- Cleanup functionality
- Error handling

Usage:
    python test_tngd_backup.py
"""

import sys
import os
import tempfile
from pathlib import Path

# Add the project root to Python path
sys.path.insert(0, str(Path(__file__).parent))

from tngd_backup import TngdBackupCLI, parse_args, determine_target_dates
from datetime import datetime


def test_cli_initialization():
    """Test CLI initialization and configuration loading."""
    print("🧪 Testing CLI initialization...")

    cli = TngdBackupCLI()
    cli.setup_logging(verbose=False)

    # Test configuration loading
    assert cli.config_manager is not None, "Config manager should be initialized"
    assert cli.logger is not None, "Logger should be initialized"

    print("✅ CLI initialization test passed")


def test_table_loading():
    """Test table configuration loading."""
    print("🧪 Testing table loading...")

    cli = TngdBackupCLI()
    cli.setup_logging(verbose=False)

    # Test loading from default configuration
    tables = cli.load_table_list()
    assert isinstance(tables, list), "Tables should be a list"
    assert len(tables) > 0, "Should load at least some tables"

    print(f"✅ Table loading test passed - loaded {len(tables)} tables")


def test_date_parsing():
    """Test date parsing functionality."""
    print("🧪 Testing date parsing...")

    cli = TngdBackupCLI()
    cli.setup_logging(verbose=False)

    # Test various date formats
    test_dates = [
        "01 March 2025",
        "01 Mar 2025",
        "2025-03-01"
    ]

    for date_str in test_dates:
        parsed_date = cli.parse_date_string(date_str)
        assert isinstance(parsed_date, datetime), f"Should parse {date_str} to datetime"
        assert parsed_date.year == 2025, f"Year should be 2025 for {date_str}"
        assert parsed_date.month == 3, f"Month should be 3 for {date_str}"
        assert parsed_date.day == 1, f"Day should be 1 for {date_str}"

    print("✅ Date parsing test passed")


def test_date_range_generation():
    """Test date range generation."""
    print("🧪 Testing date range generation...")

    cli = TngdBackupCLI()
    cli.setup_logging(verbose=False)

    start_date = datetime(2025, 3, 1)
    end_date = datetime(2025, 3, 3)

    date_range = cli.generate_date_range(start_date, end_date)

    assert len(date_range) == 3, "Should generate 3 dates"
    assert date_range[0] == start_date, "First date should match start"
    assert date_range[-1] == end_date, "Last date should match end"

    print("✅ Date range generation test passed")


def test_cleanup_functionality():
    """Test temporary file cleanup."""
    print("🧪 Testing cleanup functionality...")

    cli = TngdBackupCLI()
    cli.setup_logging(verbose=False)

    # Create a temporary file
    temp_file = tempfile.mktemp(suffix='.test')
    with open(temp_file, 'w') as f:
        f.write("test content")

    # Add to tracking
    cli.temp_files.append(temp_file)

    # Test cleanup
    cli.cleanup_temp(temp_file)

    assert not os.path.exists(temp_file), "Temporary file should be removed"
    assert temp_file not in cli.temp_files, "File should be removed from tracking"

    print("✅ Cleanup functionality test passed")


def test_oss_path_generation():
    """Test OSS path generation."""
    print("🧪 Testing OSS path generation...")

    cli = TngdBackupCLI()
    cli.setup_logging(verbose=False)

    table_name = "my.app.tngd.waf"
    target_date = datetime(2025, 3, 1)

    oss_path = cli._generate_oss_path(table_name, target_date)

    assert isinstance(oss_path, str), "OSS path should be a string"
    assert "March" in oss_path, "Path should contain month name"
    assert "2025-03-01" in oss_path, "Path should contain date"
    assert table_name in oss_path, "Path should contain table name"

    print(f"✅ OSS path generation test passed: {oss_path}")


def run_all_tests():
    """Run all tests and report results."""
    print("🚀 Starting TNGD Backup System Tests")
    print("=" * 50)
    
    tests = [
        test_cli_initialization,
        test_table_loading,
        test_date_parsing,
        test_date_range_generation,
        test_cleanup_functionality,
        test_oss_path_generation
    ]
    
    passed = 0
    failed = 0
    
    for test in tests:
        try:
            if test():
                passed += 1
            else:
                failed += 1
        except Exception as e:
            print(f"❌ Test {test.__name__} failed with exception: {e}")
            failed += 1
        print()
    
    print("=" * 50)
    print(f"📊 Test Results: {passed} passed, {failed} failed")
    
    if failed == 0:
        print("🎉 All tests passed!")
        return True
    else:
        print(f"⚠️  {failed} tests failed")
        return False


if __name__ == "__main__":
    success = run_all_tests()
    sys.exit(0 if success else 1)
