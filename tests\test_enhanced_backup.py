#!/usr/bin/env python3
"""
Test script for the enhanced backup system.
Tests the new features: chunked processing, retry logic, and skip failed tables.
"""

import sys
import json
from pathlib import Path

# Add the project root to Python path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from tngd_backup import Tngd<PERSON><PERSON><PERSON><PERSON><PERSON>

def test_configuration():
    """Test that the enhanced configuration is loaded correctly."""
    print("🔧 Testing Enhanced Configuration...")
    
    try:
        backup_cli = TngdBackupCLI()
        backup_cli.setup_logging(verbose=True)
        
        # Test timeout configuration
        timeouts = backup_cli.config_manager.get('backup', 'query_timeouts', {})
        print(f"✅ Query timeouts loaded: {timeouts}")
        
        # Test retry configuration
        retry_config = backup_cli.config_manager.get('backup', 'retry_configuration', {})
        print(f"✅ Retry configuration loaded: {retry_config}")
        
        # Test chunking configuration
        chunking_config = backup_cli.config_manager.get('backup', 'chunking_configuration', {})
        print(f"✅ Chunking configuration loaded: {chunking_config}")
        
        # Test dynamic timeout method
        timeout_large = backup_cli._get_dynamic_timeout('cef0.zscaler.nssweblog')
        timeout_normal = backup_cli._get_dynamic_timeout('my.app.tngd.waf')
        print(f"✅ Dynamic timeouts - Large table: {timeout_large}s, Normal table: {timeout_normal}s")
        
        # Test chunking decision
        should_chunk_large = backup_cli._should_use_chunked_processing('cef0.zscaler.nssweblog')
        should_chunk_normal = backup_cli._should_use_chunked_processing('my.app.tngd.waf')
        print(f"✅ Chunking decisions - Large table: {should_chunk_large}, Normal table: {should_chunk_normal}")
        
        print("✅ Configuration test passed!")
        return True
        
    except Exception as e:
        print(f"❌ Configuration test failed: {str(e)}")
        return False

def test_table_loading():
    """Test that all tables are loaded correctly."""
    print("\n📋 Testing Table Loading...")
    
    try:
        backup_cli = TngdBackupCLI()
        backup_cli.setup_logging(verbose=False)
        
        tables = backup_cli.load_table_list()
        print(f"✅ Loaded {len(tables)} tables")
        
        # Check for known problematic tables
        problematic_tables = ['cef0.zscaler.nssweblog', 'cloud.alibaba.log_service.events']
        for table in problematic_tables:
            if table in tables:
                print(f"✅ Found problematic table: {table}")
            else:
                print(f"⚠️  Problematic table not found: {table}")
        
        print("✅ Table loading test passed!")
        return True
        
    except Exception as e:
        print(f"❌ Table loading test failed: {str(e)}")
        return False

def test_client_initialization():
    """Test that all clients can be initialized."""
    print("\n🔌 Testing Client Initialization...")
    
    try:
        backup_cli = TngdBackupCLI()
        backup_cli.setup_logging(verbose=False)
        backup_cli.initialize_clients()
        
        print("✅ All clients initialized successfully!")
        return True
        
    except Exception as e:
        print(f"❌ Client initialization failed: {str(e)}")
        print("💡 This might be due to missing credentials or network issues")
        return False

def run_dry_run_test():
    """Run a dry run test to validate the system."""
    print("\n🧪 Running Dry Run Test...")
    
    try:
        # Simulate dry run arguments
        class MockArgs:
            def __init__(self):
                self.date_parts = []
                self.start = "26 march 2025"
                self.end = "26 march 2025"  # Just one day for testing
                self.config = None
                self.tables = None
                self.dry_run = True
                self.verbose = True
        
        args = MockArgs()
        
        backup_cli = TngdBackupCLI()
        backup_cli.setup_logging(verbose=True)
        
        print("✅ Dry run test setup completed!")
        print("💡 To run actual dry run: python tngd_backup.py --start '26 march 2025' --end '26 march 2025' --dry-run")
        return True
        
    except Exception as e:
        print(f"❌ Dry run test failed: {str(e)}")
        return False

def main():
    """Run all tests."""
    print("🚀 Enhanced Backup System Test Suite")
    print("=" * 50)
    
    tests = [
        ("Configuration", test_configuration),
        ("Table Loading", test_table_loading),
        ("Client Initialization", test_client_initialization),
        ("Dry Run", run_dry_run_test)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        try:
            if test_func():
                passed += 1
        except Exception as e:
            print(f"❌ {test_name} test crashed: {str(e)}")
    
    print(f"\n📊 Test Results: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed! The enhanced backup system is ready.")
        print("\n🔄 Next Steps:")
        print("1. Run dry run: python tngd_backup.py --start '26 march 2025' --end '26 march 2025' --dry-run")
        print("2. Run actual backup: python tngd_backup.py --start '26 march 2025' --end '31 march 2025'")
    else:
        print("⚠️  Some tests failed. Please check the configuration and dependencies.")
        return 1
    
    return 0

if __name__ == "__main__":
    sys.exit(main())
