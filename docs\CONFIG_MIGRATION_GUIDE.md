# Configuration Migration Guide

## Overview

This guide explains the migration from the old configuration structure to the new, refactored configuration system that follows better separation of concerns and system flow patterns.

## 🔄 **Key Changes**

### 1. **Structural Reorganization**

The configuration has been restructured from a flat, mixed-concern structure to a hierarchical, domain-separated structure:

**Old Structure:**
```json
{
  "backup": { /* mixed concerns */ },
  "storage": { /* mixed concerns */ },
  "logging": { /* basic settings */ }
}
```

**New Structure:**
```json
{
  "system": { /* system-level settings */ },
  "data_source": { /* data source configuration */ },
  "processing": { /* data processing settings */ },
  "backup_workflows": { /* workflow definitions */ },
  "error_handling": { /* centralized error handling */ },
  "resource_management": { /* resource monitoring */ },
  "storage": { /* storage operations */ },
  "observability": { /* logging, monitoring, health */ }
}
```

### 2. **Configuration Mapping**

| Old Path | New Path | Notes |
|----------|----------|-------|
| `backup.default_timeout` | `data_source.connection.timeout_seconds` | Moved to data source |
| `backup.query_timeouts` | `data_source.table_classification.timeout_mapping` | Better organization |
| `backup.table_size_thresholds` | `data_source.table_classification.size_thresholds` | Logical grouping |
| `backup.retry_configuration` | `error_handling.retry_strategy` | Centralized error handling |
| `backup.chunking_configuration` | `processing.chunking` | Processing-specific |
| `backup.performance_optimization` | `processing.performance` | Performance settings |
| `backup.daily_backup` | `backup_workflows.daily` | Workflow definition |
| `backup.monthly_backup` | `backup_workflows.monthly` | Workflow definition |
| `storage.oss_path_template` | `storage.paths.backup_template` | Clearer naming |
| `storage.temp_dir` | `storage.paths.temp_directory` | Path organization |
| `storage.compression_algorithm` | `storage.compression.algorithm` | Compression settings |
| `logging.*` | `observability.logging.*` | Observability domain |
| `performance_monitoring.*` | `observability.monitoring.*` | Unified observability |

### 3. **New Features**

#### **System Configuration**
```json
"system": {
  "environment": "production|staging|development",
  "version": "2.0.0",
  "debug_mode": false,
  "dry_run_mode": false
}
```

#### **Centralized Error Handling**
```json
"error_handling": {
  "retry_strategy": {
    "max_attempts": 5,
    "base_delay_seconds": 30,
    "exponential_backoff": true,
    "jitter_enabled": true
  },
  "circuit_breaker": {
    "enabled": true,
    "failure_threshold": 5,
    "recovery_timeout_seconds": 300
  }
}
```

#### **Resource Management**
```json
"resource_management": {
  "memory": {
    "threshold_percent": 60,
    "adaptive_processing": true
  },
  "cpu": {
    "threshold_percent": 70,
    "throttling_enabled": true
  },
  "adaptive_throttling": {
    "enabled": true,
    "dynamic_adjustment": true
  }
}
```

## 🛠️ **Migration Steps**

### Step 1: Backup Current Configuration
```bash
cp config.json config.json.backup
```

### Step 2: Update Configuration Structure
The new `config.json` has been automatically updated with the new structure. Review the changes and adjust any custom settings.

### Step 3: Update Code References
If you have custom code that accesses configuration directly, update the paths:

**Old:**
```python
timeout = config_manager.get('backup', 'default_timeout')
chunk_size = config_manager.get('backup', 'chunking_configuration', {}).get('hours_per_chunk')
```

**New:**
```python
timeout = config_manager.get('data_source', 'connection', {}).get('timeout_seconds')
chunk_size = config_manager.get('processing', 'chunking', {}).get('time_chunk_hours')
```

### Step 4: Validate Configuration
The new system includes schema validation. Run a dry-run to validate:

```bash
python tngd_backup.py --dry-run
```

## 🔍 **Configuration Validation**

The new system includes comprehensive validation:

1. **Schema Validation**: Ensures all required sections and fields are present
2. **Type Validation**: Validates data types for all configuration values
3. **Business Rule Validation**: Validates business-specific rules (e.g., threshold ranges)
4. **Environment Validation**: Ensures environment-specific settings are valid

## 🚨 **Breaking Changes**

### 1. **Configuration Access Patterns**
- Direct access to nested configuration may need updates
- Some configuration keys have been renamed for clarity

### 2. **Default Values**
- Some default values have been updated for better performance
- New defaults are more conservative and secure

### 3. **Environment Variables**
- Environment variable names remain the same
- New environment variables for enhanced features

## 🔧 **Backward Compatibility**

The `ConfigManager` class maintains backward compatibility for most common access patterns:

- `get_backup_settings()` - Maps to new structure
- `get_storage_settings()` - Updated to new storage structure
- `get_oss_credentials()` - Unchanged
- `get_email_credentials()` - Unchanged

## 📋 **Validation Checklist**

- [ ] Configuration file loads without errors
- [ ] Schema validation passes
- [ ] All required environment variables are set
- [ ] Backup workflows are properly configured
- [ ] Storage paths are accessible
- [ ] Logging configuration is valid
- [ ] Resource thresholds are within acceptable ranges

## 🆘 **Troubleshooting**

### Common Issues:

1. **Schema Validation Errors**
   - Check that all required sections are present
   - Verify data types match expected types
   - Ensure threshold values are within valid ranges

2. **Missing Configuration Values**
   - The system will use defaults for missing values
   - Check logs for warnings about missing configuration

3. **Environment Variable Issues**
   - Ensure all required environment variables are set
   - Check `.env` file if using environment file

### Getting Help:

1. Run with `--dry-run` flag to validate configuration
2. Check logs for detailed error messages
3. Review the schema validation output
4. Compare with the default configuration structure

## 📈 **Benefits of New Structure**

1. **Better Separation of Concerns**: Each section has a clear responsibility
2. **Enhanced Validation**: Comprehensive validation prevents configuration errors
3. **Improved Maintainability**: Easier to understand and modify
4. **Better Documentation**: Self-documenting structure
5. **Future-Proof**: Easier to extend with new features
6. **Security**: Better credential management and validation
7. **Performance**: Optimized configuration access patterns
