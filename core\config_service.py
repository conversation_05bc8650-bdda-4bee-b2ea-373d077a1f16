#!/usr/bin/env python3
"""
Configuration Service Module

This module provides centralized configuration access patterns to eliminate
code duplication across the TNGD backup system. It standardizes configuration
retrieval, validation, and caching.

Features:
- Centralized configuration access patterns
- Configuration caching and validation
- Type-safe configuration retrieval
- Default value management
- Configuration change notifications
- Environment-specific overrides
"""

import logging
import threading
from typing import Any, Dict, Optional, TypeVar, Type, Union, List, Callable
from dataclasses import dataclass
from enum import Enum

# Configure logging
logger = logging.getLogger(__name__)

# Type variable for generic configuration values
T = TypeVar('T')


class ConfigScope(Enum):
    """Configuration scopes for different components."""
    SYSTEM = "system"
    DATA_SOURCE = "data_source"
    PROCESSING = "processing"
    BACKUP_WORKFLOWS = "backup_workflows"
    ERROR_HANDLING = "error_handling"
    RESOURCE_MANAGEMENT = "resource_management"
    STORAGE = "storage"
    OBSERVABILITY = "observability"
    NOTIFICATIONS = "notifications"


@dataclass
class ConfigPath:
    """Represents a configuration path with scope and key."""
    scope: ConfigScope
    key: str
    subkey: Optional[str] = None
    
    def __str__(self) -> str:
        if self.subkey:
            return f"{self.scope.value}.{self.key}.{self.subkey}"
        return f"{self.scope.value}.{self.key}"


class ConfigService:
    """Centralized configuration access service."""
    
    def __init__(self, config_manager):
        """
        Initialize the configuration service.
        
        Args:
            config_manager: Configuration manager instance
        """
        self.config_manager = config_manager
        self._cache: Dict[str, Any] = {}
        self._cache_timestamps: Dict[str, float] = {}
        self._cache_ttl = 300  # 5 minutes cache TTL
        self._lock = threading.Lock()
        self._change_listeners: List[Callable[[str, Any], None]] = []
        
        logger.info("Configuration service initialized")
    
    # System Configuration
    def get_environment(self) -> str:
        """Get the current environment (development, staging, production)."""
        return self._get_config(ConfigScope.SYSTEM, "environment", "development", str)
    
    def get_version(self) -> str:
        """Get the system version."""
        return self._get_config(ConfigScope.SYSTEM, "version", "1.0.0", str)
    
    def is_debug_mode(self) -> bool:
        """Check if debug mode is enabled."""
        return self._get_config(ConfigScope.SYSTEM, "debug_mode", False, bool)
    
    def is_dry_run_mode(self) -> bool:
        """Check if dry run mode is enabled."""
        return self._get_config(ConfigScope.SYSTEM, "dry_run_mode", False, bool)
    
    # Data Source Configuration
    def get_data_source_provider(self) -> str:
        """Get the data source provider."""
        return self._get_config(ConfigScope.DATA_SOURCE, "provider", "devo", str)
    
    def get_connection_timeout(self) -> int:
        """Get the connection timeout in seconds."""
        return self._get_config_nested(ConfigScope.DATA_SOURCE, "connection", "timeout_seconds", 21600, int)
    
    def get_retry_attempts(self) -> int:
        """Get the number of retry attempts for connections."""
        return self._get_config_nested(ConfigScope.DATA_SOURCE, "connection", "retry_attempts", 3, int)
    
    def get_query_timeout(self, table_size: str) -> int:
        """Get query timeout for specific table size."""
        timeouts = self._get_config_nested(ConfigScope.DATA_SOURCE, "table_classification", "timeout_mapping", {})
        return timeouts.get(table_size, 1800)
    
    def get_table_size_threshold(self, size_category: str) -> int:
        """Get table size threshold for specific category."""
        thresholds = self._get_config_nested(ConfigScope.DATA_SOURCE, "table_classification", "size_thresholds", {})
        return thresholds.get(size_category, 100000)
    
    # Processing Configuration
    def is_chunking_enabled(self) -> bool:
        """Check if chunking is enabled."""
        return self._get_config_nested(ConfigScope.PROCESSING, "chunking", "enabled", True, bool)
    
    def get_chunking_strategy(self) -> str:
        """Get the chunking strategy."""
        return self._get_config_nested(ConfigScope.PROCESSING, "chunking", "strategy", "time_based", str)
    
    def get_chunk_time_hours(self) -> int:
        """Get the time chunk size in hours."""
        return self._get_config_nested(ConfigScope.PROCESSING, "chunking", "time_chunk_hours", 6, int)
    
    def get_max_rows_per_chunk(self) -> int:
        """Get the maximum rows per chunk."""
        return self._get_config_nested(ConfigScope.PROCESSING, "chunking", "max_rows_per_chunk", 1000000, int)
    
    def is_deduplication_enabled(self) -> bool:
        """Check if deduplication is enabled."""
        return self._get_config_nested(ConfigScope.PROCESSING, "deduplication", "enabled", True, bool)
    
    def get_deduplication_algorithm(self) -> str:
        """Get the deduplication algorithm."""
        return self._get_config_nested(ConfigScope.PROCESSING, "deduplication", "algorithm", "efficient_hash", str)
    
    # Error Handling Configuration
    def get_max_retry_attempts(self) -> int:
        """Get the maximum retry attempts."""
        return self._get_config_nested(ConfigScope.ERROR_HANDLING, "retry_strategy", "max_attempts", 5, int)
    
    def get_retry_base_delay(self) -> float:
        """Get the base delay for retries in seconds."""
        return self._get_config_nested(ConfigScope.ERROR_HANDLING, "retry_strategy", "base_delay_seconds", 30.0, float)
    
    def get_retry_max_delay(self) -> float:
        """Get the maximum delay for retries in seconds."""
        return self._get_config_nested(ConfigScope.ERROR_HANDLING, "retry_strategy", "max_delay_seconds", 300.0, float)
    
    def is_exponential_backoff_enabled(self) -> bool:
        """Check if exponential backoff is enabled."""
        return self._get_config_nested(ConfigScope.ERROR_HANDLING, "retry_strategy", "exponential_backoff", True, bool)
    
    def get_backoff_multiplier(self) -> float:
        """Get the backoff multiplier."""
        return self._get_config_nested(ConfigScope.ERROR_HANDLING, "retry_strategy", "backoff_multiplier", 2.0, float)
    
    def should_continue_on_failure(self) -> bool:
        """Check if processing should continue on failure."""
        return self._get_config_nested(ConfigScope.ERROR_HANDLING, "failure_recovery", "continue_on_table_failure", True, bool)
    
    def should_skip_failed_tables(self) -> bool:
        """Check if failed tables should be skipped."""
        return self._get_config_nested(ConfigScope.ERROR_HANDLING, "failure_recovery", "skip_failed_tables", True, bool)
    
    def is_circuit_breaker_enabled(self) -> bool:
        """Check if circuit breaker is enabled."""
        return self._get_config_nested(ConfigScope.ERROR_HANDLING, "circuit_breaker", "enabled", True, bool)
    
    def get_circuit_breaker_failure_threshold(self) -> int:
        """Get the circuit breaker failure threshold."""
        return self._get_config_nested(ConfigScope.ERROR_HANDLING, "circuit_breaker", "failure_threshold", 5, int)
    
    # Resource Management Configuration
    def get_memory_threshold(self) -> float:
        """Get the memory usage threshold percentage."""
        return self._get_config_nested(ConfigScope.RESOURCE_MANAGEMENT, "memory", "threshold_percent", 60.0, float)
    
    def get_cpu_threshold(self) -> float:
        """Get the CPU usage threshold percentage."""
        return self._get_config_nested(ConfigScope.RESOURCE_MANAGEMENT, "cpu", "threshold_percent", 70.0, float)
    
    def get_disk_threshold(self) -> float:
        """Get the disk usage threshold percentage."""
        return self._get_config_nested(ConfigScope.RESOURCE_MANAGEMENT, "disk", "threshold_percent", 80.0, float)
    
    def is_adaptive_throttling_enabled(self) -> bool:
        """Check if adaptive throttling is enabled."""
        return self._get_config_nested(ConfigScope.RESOURCE_MANAGEMENT, "adaptive_throttling", "enabled", True, bool)
    
    def get_min_throttle_delay(self) -> float:
        """Get the minimum throttle delay in seconds."""
        return self._get_config_nested(ConfigScope.RESOURCE_MANAGEMENT, "adaptive_throttling", "min_delay_seconds", 2.0, float)
    
    def get_max_throttle_delay(self) -> float:
        """Get the maximum throttle delay in seconds."""
        return self._get_config_nested(ConfigScope.RESOURCE_MANAGEMENT, "adaptive_throttling", "max_delay_seconds", 60.0, float)
    
    # Storage Configuration
    def get_storage_provider(self) -> str:
        """Get the storage provider."""
        return self._get_config(ConfigScope.STORAGE, "provider", "oss", str)
    
    def get_backup_path_template(self) -> str:
        """Get the backup path template."""
        return self._get_config_nested(ConfigScope.STORAGE, "paths", "backup_template", 
                                     "Devo/{month_name_str}/week {week_number}/{date_str}/{table_name}_{date_str}.tar.gz", str)
    
    def get_temp_directory(self) -> str:
        """Get the temporary directory path."""
        return self._get_config_nested(ConfigScope.STORAGE, "paths", "temp_directory", "temp", str)
    
    def get_compression_algorithm(self) -> str:
        """Get the compression algorithm."""
        return self._get_config_nested(ConfigScope.STORAGE, "compression", "algorithm", "tar.gz", str)
    
    def get_compression_level(self) -> int:
        """Get the compression level."""
        return self._get_config_nested(ConfigScope.STORAGE, "compression", "level", 6, int)
    
    def is_compression_verification_enabled(self) -> bool:
        """Check if compression verification is enabled."""
        return self._get_config_nested(ConfigScope.STORAGE, "compression", "verification_enabled", True, bool)
    
    # Observability Configuration
    def get_log_directory(self) -> str:
        """Get the log directory path."""
        return self._get_config_nested(ConfigScope.OBSERVABILITY, "logging", "directory", "logs", str)
    
    def get_log_file_name(self) -> str:
        """Get the primary log file name."""
        return self._get_config_nested(ConfigScope.OBSERVABILITY, "logging", "primary_log_file", "backup.log", str)
    
    def get_max_log_files(self) -> int:
        """Get the maximum number of log files to keep."""
        return self._get_config_nested(ConfigScope.OBSERVABILITY, "logging", "max_log_files", 5, int)
    
    def get_max_log_file_size_mb(self) -> int:
        """Get the maximum log file size in MB."""
        return self._get_config_nested(ConfigScope.OBSERVABILITY, "logging", "max_file_size_mb", 10, int)
    
    def is_monitoring_enabled(self) -> bool:
        """Check if monitoring is enabled."""
        return self._get_config_nested(ConfigScope.OBSERVABILITY, "monitoring", "enabled", True, bool)
    
    def is_health_checks_enabled(self) -> bool:
        """Check if health checks are enabled."""
        return self._get_config_nested(ConfigScope.OBSERVABILITY, "health_checks", "enabled", True, bool)
    
    # Workflow Configuration
    def is_daily_backup_enabled(self) -> bool:
        """Check if daily backup workflow is enabled."""
        return self._get_config_nested(ConfigScope.BACKUP_WORKFLOWS, "daily", "enabled", True, bool)
    
    def is_monthly_backup_enabled(self) -> bool:
        """Check if monthly backup workflow is enabled."""
        return self._get_config_nested(ConfigScope.BACKUP_WORKFLOWS, "monthly", "enabled", True, bool)
    
    def get_daily_backup_schedule_time(self) -> str:
        """Get the daily backup schedule time."""
        return self._get_config_nested(ConfigScope.BACKUP_WORKFLOWS, "daily", "schedule_time", "01:00", str)
    
    def get_monthly_backup_chunking_strategy(self) -> str:
        """Get the monthly backup chunking strategy."""
        return self._get_config_nested(ConfigScope.BACKUP_WORKFLOWS, "monthly", "chunking_strategy", "daily", str)
    
    # Private helper methods
    def _get_config(self, scope: ConfigScope, key: str, default: T, expected_type: Type[T]) -> T:
        """Get a configuration value with type checking."""
        config_path = f"{scope.value}.{key}"
        
        try:
            value = self.config_manager.get(scope.value, key, default)
            
            # Type validation
            if not isinstance(value, expected_type):
                logger.warning(f"Configuration value {config_path} has incorrect type. "
                             f"Expected {expected_type.__name__}, got {type(value).__name__}. Using default.")
                return default
            
            return value
        except Exception as e:
            logger.error(f"Error retrieving configuration {config_path}: {e}. Using default.")
            return default
    
    def _get_config_nested(self, scope: ConfigScope, key: str, subkey: str, default: T, expected_type: Type[T] = None) -> T:
        """Get a nested configuration value with type checking."""
        config_path = f"{scope.value}.{key}.{subkey}"
        
        try:
            section = self.config_manager.get(scope.value, key, {})
            if not isinstance(section, dict):
                logger.warning(f"Configuration section {scope.value}.{key} is not a dictionary. Using default for {subkey}.")
                return default
            
            value = section.get(subkey, default)
            
            # Type validation if expected_type is provided
            if expected_type and not isinstance(value, expected_type):
                logger.warning(f"Configuration value {config_path} has incorrect type. "
                             f"Expected {expected_type.__name__}, got {type(value).__name__}. Using default.")
                return default
            
            return value
        except Exception as e:
            logger.error(f"Error retrieving nested configuration {config_path}: {e}. Using default.")
            return default
    
    def get_all_config_for_scope(self, scope: ConfigScope) -> Dict[str, Any]:
        """Get all configuration for a specific scope."""
        try:
            return self.config_manager.config.get(scope.value, {})
        except Exception as e:
            logger.error(f"Error retrieving configuration for scope {scope.value}: {e}")
            return {}
    
    def validate_configuration(self) -> List[str]:
        """Validate configuration and return list of issues."""
        issues = []
        
        # Validate critical configuration values
        critical_configs = [
            (ConfigScope.DATA_SOURCE, "provider"),
            (ConfigScope.STORAGE, "provider"),
            (ConfigScope.OBSERVABILITY, "logging")
        ]
        
        for scope, key in critical_configs:
            try:
                value = self.config_manager.get(scope.value, key)
                if value is None:
                    issues.append(f"Missing critical configuration: {scope.value}.{key}")
            except Exception as e:
                issues.append(f"Error accessing configuration {scope.value}.{key}: {e}")
        
        return issues
    
    def register_change_listener(self, listener: Callable[[str, Any], None]):
        """Register a listener for configuration changes."""
        self._change_listeners.append(listener)
    
    def notify_change(self, config_path: str, new_value: Any):
        """Notify listeners of configuration changes."""
        for listener in self._change_listeners:
            try:
                listener(config_path, new_value)
            except Exception as e:
                logger.error(f"Error notifying configuration change listener: {e}")
