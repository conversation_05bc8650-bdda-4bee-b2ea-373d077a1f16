#!/usr/bin/env python3
"""
Unified Retry Service Module

This module provides centralized retry logic to eliminate code duplication across
the TNGD backup system. It implements configurable retry strategies with
exponential backoff, jitter, and circuit breaker patterns.

Features:
- Configurable retry strategies
- Exponential backoff with jitter
- Circuit breaker pattern
- Retryable error detection
- Comprehensive logging and metrics
- Thread-safe operations
"""

import time
import random
import logging
import threading
from typing import Callable, Any, Dict, Optional, List, Tuple, TypeVar, Union
from dataclasses import dataclass, field
from enum import Enum
from datetime import datetime, timedelta

# Configure logging
logger = logging.getLogger(__name__)

# Type variable for generic return types
T = TypeVar('T')


class RetryStrategy(Enum):
    """Supported retry strategies."""
    FIXED_DELAY = "fixed_delay"
    EXPONENTIAL_BACKOFF = "exponential_backoff"
    LINEAR_BACKOFF = "linear_backoff"
    FIBONACCI_BACKOFF = "fibonacci_backoff"


class CircuitBreakerState(Enum):
    """Circuit breaker states."""
    CLOSED = "closed"      # Normal operation
    OPEN = "open"          # Failing, rejecting calls
    HALF_OPEN = "half_open"  # Testing if service recovered


@dataclass
class RetryConfig:
    """Configuration for retry operations."""
    max_attempts: int = 3
    strategy: RetryStrategy = RetryStrategy.EXPONENTIAL_BACKOFF
    base_delay_seconds: float = 1.0
    max_delay_seconds: float = 300.0
    backoff_multiplier: float = 2.0
    jitter_enabled: bool = True
    jitter_range: Tuple[float, float] = (0.8, 1.2)
    retryable_exceptions: Tuple[type, ...] = (Exception,)
    non_retryable_exceptions: Tuple[type, ...] = ()
    retryable_error_keywords: List[str] = field(default_factory=lambda: [
        'timeout', 'connection', 'network', 'temporary', 'throttle',
        'rate limit', 'service unavailable', 'internal server error',
        'connection broken', 'invalid chunk length'
    ])


@dataclass
class CircuitBreakerConfig:
    """Configuration for circuit breaker."""
    enabled: bool = True
    failure_threshold: int = 5
    recovery_timeout_seconds: float = 300.0
    half_open_max_calls: int = 3
    success_threshold: int = 2  # Successes needed to close circuit


@dataclass
class RetryResult:
    """Result of a retry operation."""
    success: bool
    result: Any = None
    error: Optional[Exception] = None
    attempts_made: int = 0
    total_delay: float = 0.0
    circuit_breaker_triggered: bool = False


class CircuitBreaker:
    """Circuit breaker implementation for retry operations."""
    
    def __init__(self, config: CircuitBreakerConfig):
        self.config = config
        self.state = CircuitBreakerState.CLOSED
        self.failure_count = 0
        self.success_count = 0
        self.last_failure_time: Optional[datetime] = None
        self.half_open_calls = 0
        self._lock = threading.Lock()
    
    def can_execute(self) -> bool:
        """Check if operation can be executed."""
        if not self.config.enabled:
            return True
            
        with self._lock:
            if self.state == CircuitBreakerState.CLOSED:
                return True
            elif self.state == CircuitBreakerState.OPEN:
                # Check if recovery timeout has passed
                if (self.last_failure_time and 
                    datetime.now() - self.last_failure_time >= 
                    timedelta(seconds=self.config.recovery_timeout_seconds)):
                    self.state = CircuitBreakerState.HALF_OPEN
                    self.half_open_calls = 0
                    logger.info("Circuit breaker transitioning to HALF_OPEN state")
                    return True
                return False
            else:  # HALF_OPEN
                return self.half_open_calls < self.config.half_open_max_calls
    
    def record_success(self):
        """Record a successful operation."""
        if not self.config.enabled:
            return
            
        with self._lock:
            if self.state == CircuitBreakerState.HALF_OPEN:
                self.success_count += 1
                if self.success_count >= self.config.success_threshold:
                    self.state = CircuitBreakerState.CLOSED
                    self.failure_count = 0
                    self.success_count = 0
                    logger.info("Circuit breaker closed - service recovered")
            else:
                self.failure_count = 0  # Reset failure count on success
    
    def record_failure(self):
        """Record a failed operation."""
        if not self.config.enabled:
            return
            
        with self._lock:
            self.failure_count += 1
            self.last_failure_time = datetime.now()
            
            if self.state == CircuitBreakerState.CLOSED:
                if self.failure_count >= self.config.failure_threshold:
                    self.state = CircuitBreakerState.OPEN
                    logger.warning(f"Circuit breaker opened after {self.failure_count} failures")
            elif self.state == CircuitBreakerState.HALF_OPEN:
                self.state = CircuitBreakerState.OPEN
                self.half_open_calls = 0
                logger.warning("Circuit breaker reopened during half-open state")
            
            if self.state == CircuitBreakerState.HALF_OPEN:
                self.half_open_calls += 1


class RetryService:
    """Unified retry service for all retry operations in the system."""
    
    def __init__(self, config_manager=None):
        """
        Initialize the retry service.
        
        Args:
            config_manager: Configuration manager instance
        """
        self.config_manager = config_manager
        self._circuit_breakers: Dict[str, CircuitBreaker] = {}
        self._lock = threading.Lock()
        
        # Load default configurations
        self.default_retry_config = self._load_retry_config()
        self.default_circuit_breaker_config = self._load_circuit_breaker_config()
    
    def _load_retry_config(self) -> RetryConfig:
        """Load retry configuration from config manager."""
        if not self.config_manager:
            return RetryConfig()
        
        retry_config = self.config_manager.get('error_handling', 'retry_strategy', {})
        
        return RetryConfig(
            max_attempts=retry_config.get('max_attempts', 3),
            base_delay_seconds=retry_config.get('base_delay_seconds', 1.0),
            max_delay_seconds=retry_config.get('max_delay_seconds', 300.0),
            backoff_multiplier=retry_config.get('backoff_multiplier', 2.0),
            jitter_enabled=retry_config.get('jitter_enabled', True)
        )
    
    def _load_circuit_breaker_config(self) -> CircuitBreakerConfig:
        """Load circuit breaker configuration from config manager."""
        if not self.config_manager:
            return CircuitBreakerConfig()
        
        cb_config = self.config_manager.get('error_handling', 'circuit_breaker', {})
        
        return CircuitBreakerConfig(
            enabled=cb_config.get('enabled', True),
            failure_threshold=cb_config.get('failure_threshold', 5),
            recovery_timeout_seconds=cb_config.get('recovery_timeout_seconds', 300.0),
            half_open_max_calls=cb_config.get('half_open_max_calls', 3)
        )
    
    def get_circuit_breaker(self, name: str) -> CircuitBreaker:
        """Get or create a circuit breaker for the given name."""
        with self._lock:
            if name not in self._circuit_breakers:
                self._circuit_breakers[name] = CircuitBreaker(self.default_circuit_breaker_config)
            return self._circuit_breakers[name]
    
    def execute_with_retry(self, 
                          operation: Callable[[], T],
                          operation_name: str = "operation",
                          retry_config: Optional[RetryConfig] = None,
                          circuit_breaker_name: Optional[str] = None) -> RetryResult:
        """
        Execute an operation with retry logic and optional circuit breaker.
        
        Args:
            operation: Function to execute
            operation_name: Name for logging purposes
            retry_config: Custom retry configuration (uses default if None)
            circuit_breaker_name: Name for circuit breaker (disabled if None)
        
        Returns:
            RetryResult with operation outcome
        """
        config = retry_config or self.default_retry_config
        circuit_breaker = self.get_circuit_breaker(circuit_breaker_name) if circuit_breaker_name else None
        
        result = RetryResult(success=False, attempts_made=0, total_delay=0.0)
        last_error = None
        
        for attempt in range(config.max_attempts):
            result.attempts_made = attempt + 1
            
            # Check circuit breaker
            if circuit_breaker and not circuit_breaker.can_execute():
                result.circuit_breaker_triggered = True
                result.error = Exception(f"Circuit breaker is OPEN for {operation_name}")
                logger.warning(f"Circuit breaker prevented execution of {operation_name}")
                break
            
            try:
                if attempt > 0:
                    delay = self._calculate_delay(attempt - 1, config)
                    logger.info(f"{operation_name} retry attempt {attempt + 1}/{config.max_attempts} after {delay:.1f}s delay")
                    time.sleep(delay)
                    result.total_delay += delay
                else:
                    logger.debug(f"Executing {operation_name} (attempt 1/{config.max_attempts})")
                
                # Execute the operation
                operation_result = operation()
                
                # Success
                result.success = True
                result.result = operation_result
                
                if circuit_breaker:
                    circuit_breaker.record_success()
                
                if attempt > 0:
                    logger.info(f"{operation_name} succeeded on attempt {attempt + 1}")
                
                return result
                
            except Exception as e:
                last_error = e
                result.error = e
                
                # Check if error is retryable
                if not self._is_retryable_error(e, config):
                    logger.error(f"{operation_name} failed with non-retryable error: {str(e)}")
                    if circuit_breaker:
                        circuit_breaker.record_failure()
                    break
                
                if attempt < config.max_attempts - 1:
                    logger.warning(f"{operation_name} attempt {attempt + 1} failed: {str(e)}")
                else:
                    logger.error(f"{operation_name} failed after {config.max_attempts} attempts: {str(e)}")
                
                if circuit_breaker:
                    circuit_breaker.record_failure()
        
        return result
    
    def _calculate_delay(self, attempt: int, config: RetryConfig) -> float:
        """Calculate delay for retry attempt."""
        if config.strategy == RetryStrategy.FIXED_DELAY:
            delay = config.base_delay_seconds
        elif config.strategy == RetryStrategy.EXPONENTIAL_BACKOFF:
            delay = config.base_delay_seconds * (config.backoff_multiplier ** attempt)
        elif config.strategy == RetryStrategy.LINEAR_BACKOFF:
            delay = config.base_delay_seconds * (attempt + 1)
        elif config.strategy == RetryStrategy.FIBONACCI_BACKOFF:
            delay = config.base_delay_seconds * self._fibonacci(attempt + 1)
        else:
            delay = config.base_delay_seconds
        
        # Apply maximum delay limit
        delay = min(delay, config.max_delay_seconds)
        
        # Apply jitter if enabled
        if config.jitter_enabled:
            jitter_min, jitter_max = config.jitter_range
            jitter = random.uniform(jitter_min, jitter_max)
            delay *= jitter
        
        return delay
    
    def _fibonacci(self, n: int) -> int:
        """Calculate nth Fibonacci number."""
        if n <= 1:
            return n
        a, b = 0, 1
        for _ in range(2, n + 1):
            a, b = b, a + b
        return b
    
    def _is_retryable_error(self, error: Exception, config: RetryConfig) -> bool:
        """Determine if an error is retryable based on configuration."""
        # Check non-retryable exceptions first
        if config.non_retryable_exceptions and isinstance(error, config.non_retryable_exceptions):
            return False
        
        # Check retryable exceptions
        if config.retryable_exceptions and isinstance(error, config.retryable_exceptions):
            # Check error message for retryable keywords
            error_str = str(error).lower()
            return any(keyword in error_str for keyword in config.retryable_error_keywords)
        
        return False
    
    def get_stats(self) -> Dict[str, Any]:
        """Get retry service statistics."""
        with self._lock:
            circuit_breaker_stats = {}
            for name, cb in self._circuit_breakers.items():
                circuit_breaker_stats[name] = {
                    'state': cb.state.value,
                    'failure_count': cb.failure_count,
                    'success_count': cb.success_count,
                    'last_failure_time': cb.last_failure_time.isoformat() if cb.last_failure_time else None
                }
            
            return {
                'circuit_breakers': circuit_breaker_stats,
                'total_circuit_breakers': len(self._circuit_breakers)
            }
