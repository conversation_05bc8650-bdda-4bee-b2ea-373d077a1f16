#!/usr/bin/env python3
"""
Configuration Schema Validation Module

This module provides schema validation for the refactored configuration structure.
It ensures configuration integrity and provides clear error messages for invalid configs.
"""

from typing import Dict, Any, List, Optional
import logging
from dataclasses import dataclass
from enum import Enum

logger = logging.getLogger(__name__)


class Environment(Enum):
    """Supported environment types."""
    DEVELOPMENT = "development"
    STAGING = "staging"
    PRODUCTION = "production"


class CompressionAlgorithm(Enum):
    """Supported compression algorithms."""
    TAR_GZ = "tar.gz"
    TAR_BZ2 = "tar.bz2"
    ZIP = "zip"


class ChunkingStrategy(Enum):
    """Supported chunking strategies."""
    TIME_BASED = "time_based"
    ROW_BASED = "row_based"
    DAILY = "daily"
    HOURLY = "hourly"


@dataclass
class ConfigValidationError(Exception):
    """Configuration validation error with detailed context."""
    message: str
    path: str
    expected_type: Optional[str] = None
    actual_value: Optional[Any] = None


class ConfigSchema:
    """Configuration schema validator for the TNGD backup system."""
    
    # Required configuration sections
    REQUIRED_SECTIONS = [
        "system",
        "data_source", 
        "processing",
        "backup_workflows",
        "error_handling",
        "resource_management",
        "storage",
        "observability"
    ]
    
    # Schema definitions for each section
    SCHEMA = {
        "system": {
            "required": ["environment", "version"],
            "optional": ["debug_mode", "dry_run_mode"],
            "types": {
                "environment": str,
                "version": str,
                "debug_mode": bool,
                "dry_run_mode": bool
            }
        },
        "data_source": {
            "required": ["provider", "connection"],
            "optional": ["query_optimization", "table_classification"],
            "types": {
                "provider": str,
                "connection": dict,
                "query_optimization": dict,
                "table_classification": dict
            }
        },
        "processing": {
            "required": ["chunking", "deduplication"],
            "optional": ["performance"],
            "types": {
                "chunking": dict,
                "deduplication": dict,
                "performance": dict
            }
        },
        "backup_workflows": {
            "required": [],
            "optional": ["daily", "monthly"],
            "types": {
                "daily": dict,
                "monthly": dict
            }
        },
        "error_handling": {
            "required": ["retry_strategy", "failure_recovery"],
            "optional": ["circuit_breaker"],
            "types": {
                "retry_strategy": dict,
                "failure_recovery": dict,
                "circuit_breaker": dict
            }
        },
        "resource_management": {
            "required": ["memory", "cpu", "disk"],
            "optional": ["adaptive_throttling"],
            "types": {
                "memory": dict,
                "cpu": dict,
                "disk": dict,
                "adaptive_throttling": dict
            }
        },
        "storage": {
            "required": ["provider", "paths", "compression"],
            "optional": ["upload", "validation"],
            "types": {
                "provider": str,
                "paths": dict,
                "compression": dict,
                "upload": dict,
                "validation": dict
            }
        },
        "observability": {
            "required": ["logging"],
            "optional": ["monitoring", "health_checks"],
            "types": {
                "logging": dict,
                "monitoring": dict,
                "health_checks": dict
            }
        }
    }

    @classmethod
    def validate_config(cls, config: Dict[str, Any]) -> List[ConfigValidationError]:
        """
        Validate configuration against schema.
        
        Args:
            config: Configuration dictionary to validate
            
        Returns:
            List of validation errors (empty if valid)
        """
        errors = []
        
        # Check required sections
        for section in cls.REQUIRED_SECTIONS:
            if section not in config:
                errors.append(ConfigValidationError(
                    message=f"Required section '{section}' is missing",
                    path=section
                ))
                continue
                
            # Validate section structure
            section_errors = cls._validate_section(
                config[section], 
                cls.SCHEMA[section], 
                section
            )
            errors.extend(section_errors)
        
        # Validate specific business rules
        business_rule_errors = cls._validate_business_rules(config)
        errors.extend(business_rule_errors)
        
        return errors

    @classmethod
    def _validate_section(cls, section_config: Dict[str, Any], 
                         schema: Dict[str, Any], section_path: str) -> List[ConfigValidationError]:
        """Validate a configuration section against its schema."""
        errors = []
        
        # Check required fields
        for field in schema.get("required", []):
            if field not in section_config:
                errors.append(ConfigValidationError(
                    message=f"Required field '{field}' is missing",
                    path=f"{section_path}.{field}"
                ))
        
        # Check field types
        for field, expected_type in schema.get("types", {}).items():
            if field in section_config:
                actual_value = section_config[field]
                if not isinstance(actual_value, expected_type):
                    errors.append(ConfigValidationError(
                        message=f"Field '{field}' has incorrect type",
                        path=f"{section_path}.{field}",
                        expected_type=expected_type.__name__,
                        actual_value=actual_value
                    ))
        
        return errors

    @classmethod
    def _validate_business_rules(cls, config: Dict[str, Any]) -> List[ConfigValidationError]:
        """Validate business-specific configuration rules."""
        errors = []
        
        # Validate environment
        if "system" in config and "environment" in config["system"]:
            env = config["system"]["environment"]
            if env not in [e.value for e in Environment]:
                errors.append(ConfigValidationError(
                    message=f"Invalid environment '{env}'",
                    path="system.environment",
                    expected_type="one of: " + ", ".join([e.value for e in Environment])
                ))
        
        # Validate compression algorithm
        if ("storage" in config and "compression" in config["storage"] 
            and "algorithm" in config["storage"]["compression"]):
            algorithm = config["storage"]["compression"]["algorithm"]
            if algorithm not in [a.value for a in CompressionAlgorithm]:
                errors.append(ConfigValidationError(
                    message=f"Invalid compression algorithm '{algorithm}'",
                    path="storage.compression.algorithm",
                    expected_type="one of: " + ", ".join([a.value for a in CompressionAlgorithm])
                ))
        
        # Validate chunking strategy
        if ("processing" in config and "chunking" in config["processing"] 
            and "strategy" in config["processing"]["chunking"]):
            strategy = config["processing"]["chunking"]["strategy"]
            if strategy not in [s.value for s in ChunkingStrategy]:
                errors.append(ConfigValidationError(
                    message=f"Invalid chunking strategy '{strategy}'",
                    path="processing.chunking.strategy",
                    expected_type="one of: " + ", ".join([s.value for s in ChunkingStrategy])
                ))
        
        # Validate threshold values
        errors.extend(cls._validate_thresholds(config))
        
        return errors

    @classmethod
    def _validate_thresholds(cls, config: Dict[str, Any]) -> List[ConfigValidationError]:
        """Validate threshold values are within acceptable ranges."""
        errors = []
        
        # Memory thresholds
        if ("resource_management" in config and "memory" in config["resource_management"]):
            memory_config = config["resource_management"]["memory"]
            
            if "threshold_percent" in memory_config:
                threshold = memory_config["threshold_percent"]
                if not (0 < threshold <= 100):
                    errors.append(ConfigValidationError(
                        message=f"Memory threshold must be between 1-100, got {threshold}",
                        path="resource_management.memory.threshold_percent"
                    ))
            
            if "critical_threshold_percent" in memory_config:
                critical = memory_config["critical_threshold_percent"]
                if not (0 < critical <= 100):
                    errors.append(ConfigValidationError(
                        message=f"Critical memory threshold must be between 1-100, got {critical}",
                        path="resource_management.memory.critical_threshold_percent"
                    ))
        
        return errors

    @classmethod
    def get_default_config(cls) -> Dict[str, Any]:
        """Get a default configuration that passes validation."""
        return {
            "system": {
                "environment": "development",
                "version": "2.0.0",
                "debug_mode": False,
                "dry_run_mode": False
            },
            "data_source": {
                "provider": "devo",
                "connection": {
                    "timeout_seconds": 21600,
                    "retry_attempts": 3,
                    "retry_delay_seconds": 5
                }
            },
            "processing": {
                "chunking": {
                    "enabled": True,
                    "strategy": "time_based"
                },
                "deduplication": {
                    "enabled": True,
                    "algorithm": "efficient_hash"
                }
            },
            "backup_workflows": {},
            "error_handling": {
                "retry_strategy": {
                    "max_attempts": 5,
                    "base_delay_seconds": 30
                },
                "failure_recovery": {
                    "continue_on_table_failure": True
                }
            },
            "resource_management": {
                "memory": {"threshold_percent": 60},
                "cpu": {"threshold_percent": 70},
                "disk": {"threshold_percent": 80}
            },
            "storage": {
                "provider": "oss",
                "paths": {"temp_directory": "temp"},
                "compression": {"algorithm": "tar.gz"}
            },
            "observability": {
                "logging": {
                    "directory": "logs",
                    "primary_log_file": "backup.log"
                }
            }
        }
