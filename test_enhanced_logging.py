#!/usr/bin/env python3
"""
Test script to demonstrate enhanced data retrieval logging.
Run this to see the improved logging in action.
"""

import sys
import os
from datetime import datetime, timed<PERSON><PERSON>

# Add the current directory to the path so we can import our modules
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from tngd_backup import TngdBackupCLI

def test_enhanced_logging():
    """Test the enhanced logging functionality."""
    print("🧪 Testing Enhanced Data Retrieval Logging")
    print("=" * 50)
    
    # Initialize the backup CLI
    backup_cli = TngdBackupCLI()
    
    # Setup logging with enhanced data logging enabled
    backup_cli.setup_logging(verbose=True, data_logging=True)
    
    print("\n📊 Testing different data retrieval scenarios:")
    print("-" * 40)
    
    # Test 1: Small data retrieval
    print("\n1. Small Data Retrieval Test:")
    backup_cli.log_data_retrieval(
        operation="QUERY",
        table_name="test.small.table",
        row_count=150,
        duration=2.5,
        additional_info={"chunk": 1, "total_chunks": 1}
    )
    
    # Test 2: Medium data retrieval
    print("\n2. Medium Data Retrieval Test:")
    backup_cli.log_data_retrieval(
        operation="CHUNK",
        table_name="test.medium.table",
        row_count=25000,
        duration=15.3,
        additional_info={"chunk": 2, "total_chunks": 5}
    )
    
    # Test 3: Large data retrieval
    print("\n3. Large Data Retrieval Test:")
    backup_cli.log_data_retrieval(
        operation="BACKUP",
        table_name="test.large.table",
        row_count=500000,
        duration=120.7,
        additional_info={"method": "chunked", "memory_optimized": True}
    )
    
    # Test 4: No data found
    print("\n4. No Data Found Test:")
    backup_cli.log_data_retrieval(
        operation="QUERY",
        table_name="test.empty.table",
        row_count=0,
        duration=3.2,
        additional_info={"date_range": "2025-03-01 to 2025-03-02"}
    )
    
    # Test 5: Query without duration
    print("\n5. Query Without Duration Test:")
    backup_cli.log_data_retrieval(
        operation="VALIDATION",
        table_name="test.validation.table",
        row_count=1000,
        additional_info={"validation_type": "schema_check"}
    )
    
    print("\n" + "=" * 50)
    print("✅ Enhanced logging test completed!")
    print("📄 Check the logs/tngd_backup.log file for detailed output")

if __name__ == "__main__":
    test_enhanced_logging()
